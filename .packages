# This file is deprecated. Tools should instead consume 
# `.dart_tool/package_config.json`.
# 
# For more info see: https://dart.dev/go/dot-packages-deprecation
# 
# Generated by pub on 2022-11-22 18:04:02.517724.
async:file:///Users/<USER>/flutter/.pub-cache/hosted/pub.dartlang.org/async-2.8.2/lib/
boolean_selector:file:///Users/<USER>/flutter/.pub-cache/hosted/pub.dartlang.org/boolean_selector-2.1.0/lib/
characters:file:///Users/<USER>/flutter/.pub-cache/hosted/pub.dartlang.org/characters-1.2.0/lib/
charcode:file:///Users/<USER>/flutter/.pub-cache/hosted/pub.dartlang.org/charcode-1.3.1/lib/
clock:file:///Users/<USER>/flutter/.pub-cache/hosted/pub.dartlang.org/clock-1.1.0/lib/
collection:file:///Users/<USER>/flutter/.pub-cache/hosted/pub.dartlang.org/collection-1.16.0/lib/
cupertino_icons:file:///Users/<USER>/flutter/.pub-cache/hosted/pub.dartlang.org/cupertino_icons-1.0.5/lib/
fake_async:file:///Users/<USER>/flutter/.pub-cache/hosted/pub.dartlang.org/fake_async-1.3.0/lib/
flutter:file:///Users/<USER>/flutter/packages/flutter/lib/
flutter_easyloading:file:///Users/<USER>/flutter/.pub-cache/hosted/pub.dartlang.org/flutter_easyloading-3.0.5/lib/
flutter_lints:file:///Users/<USER>/flutter/.pub-cache/hosted/pub.dartlang.org/flutter_lints-2.0.1/lib/
flutter_spinkit:file:///Users/<USER>/flutter/.pub-cache/hosted/pub.dartlang.org/flutter_spinkit-5.1.0/lib/
flutter_test:file:///Users/<USER>/flutter/packages/flutter_test/lib/
flutter_web_plugins:file:///Users/<USER>/flutter/packages/flutter_web_plugins/lib/
http:file:///Users/<USER>/flutter/.pub-cache/hosted/pub.dartlang.org/http-0.13.4/lib/
http_parser:file:///Users/<USER>/flutter/.pub-cache/hosted/pub.dartlang.org/http_parser-4.0.1/lib/
js:file:///Users/<USER>/flutter/.pub-cache/hosted/pub.dartlang.org/js-0.6.4/lib/
lints:file:///Users/<USER>/flutter/.pub-cache/hosted/pub.dartlang.org/lints-2.0.0/lib/
matcher:file:///Users/<USER>/flutter/.pub-cache/hosted/pub.dartlang.org/matcher-0.12.11/lib/
material_color_utilities:file:///Users/<USER>/flutter/.pub-cache/hosted/pub.dartlang.org/material_color_utilities-0.1.4/lib/
meta:file:///Users/<USER>/flutter/.pub-cache/hosted/pub.dartlang.org/meta-1.7.0/lib/
path:file:///Users/<USER>/flutter/.pub-cache/hosted/pub.dartlang.org/path-1.8.1/lib/
plugin_platform_interface:file:///Users/<USER>/flutter/.pub-cache/hosted/pub.dartlang.org/plugin_platform_interface-2.1.2/lib/
responsive_framework:file:///Users/<USER>/flutter/.pub-cache/hosted/pub.dartlang.org/responsive_framework-0.2.0/lib/
sky_engine:file:///Users/<USER>/flutter/bin/cache/pkg/sky_engine/lib/
source_span:file:///Users/<USER>/flutter/.pub-cache/hosted/pub.dartlang.org/source_span-1.8.2/lib/
stack_trace:file:///Users/<USER>/flutter/.pub-cache/hosted/pub.dartlang.org/stack_trace-1.10.0/lib/
stream_channel:file:///Users/<USER>/flutter/.pub-cache/hosted/pub.dartlang.org/stream_channel-2.1.0/lib/
string_scanner:file:///Users/<USER>/flutter/.pub-cache/hosted/pub.dartlang.org/string_scanner-1.1.0/lib/
term_glyph:file:///Users/<USER>/flutter/.pub-cache/hosted/pub.dartlang.org/term_glyph-1.2.0/lib/
test_api:file:///Users/<USER>/flutter/.pub-cache/hosted/pub.dartlang.org/test_api-0.4.9/lib/
typed_data:file:///Users/<USER>/flutter/.pub-cache/hosted/pub.dartlang.org/typed_data-1.3.1/lib/
uni_links:file:///Users/<USER>/flutter/.pub-cache/hosted/pub.dartlang.org/uni_links-0.5.1/lib/
uni_links_platform_interface:file:///Users/<USER>/flutter/.pub-cache/hosted/pub.dartlang.org/uni_links_platform_interface-1.0.0/lib/
uni_links_web:file:///Users/<USER>/flutter/.pub-cache/hosted/pub.dartlang.org/uni_links_web-0.1.0/lib/
url_launcher:file:///Users/<USER>/flutter/.pub-cache/hosted/pub.dartlang.org/url_launcher-6.1.4/lib/
url_launcher_android:file:///Users/<USER>/flutter/.pub-cache/hosted/pub.dartlang.org/url_launcher_android-6.0.17/lib/
url_launcher_ios:file:///Users/<USER>/flutter/.pub-cache/hosted/pub.dartlang.org/url_launcher_ios-6.0.17/lib/
url_launcher_linux:file:///Users/<USER>/flutter/.pub-cache/hosted/pub.dartlang.org/url_launcher_linux-3.0.1/lib/
url_launcher_macos:file:///Users/<USER>/flutter/.pub-cache/hosted/pub.dartlang.org/url_launcher_macos-3.0.1/lib/
url_launcher_platform_interface:file:///Users/<USER>/flutter/.pub-cache/hosted/pub.dartlang.org/url_launcher_platform_interface-2.1.0/lib/
url_launcher_web:file:///Users/<USER>/flutter/.pub-cache/hosted/pub.dartlang.org/url_launcher_web-2.0.12/lib/
url_launcher_windows:file:///Users/<USER>/flutter/.pub-cache/hosted/pub.dartlang.org/url_launcher_windows-3.0.1/lib/
vector_math:file:///Users/<USER>/flutter/.pub-cache/hosted/pub.dartlang.org/vector_math-2.1.2/lib/
vosvguardfluttersample:lib/
