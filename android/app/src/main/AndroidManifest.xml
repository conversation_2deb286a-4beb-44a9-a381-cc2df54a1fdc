<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.example.vtap">
    <uses-permission android:name="android.permission.READ_PHONE_STATE" tools:node="replace"/>
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.GET_TASKS" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission
        android:name="android.permission.FOREGROUND_SERVICE" />

    <queries>
        <intent>
            <action android:name="android.intent.action.MAIN" />
        </intent>
    </queries>

   <application
        android:name="com.example.vtap.MainApplication"
        android:icon="@mipmap/ic_launcher"
       android:allowBackup="true"
       android:fullBackupContent="true"
       android:fullBackupOnly="true"
       android:requestLegacyExternalStorage="true"
       android:extractNativeLibs="true"
       android:zygotePreloadName="vkey.android.vos.AppZygote">
        <activity
            android:name="com.example.vtap.MainActivity"
            android:exported="true"
            android:theme="@style/LaunchTheme"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:windowSoftInputMode="adjustResize">
            <!-- Specifies an Android theme to apply to this Activity as soon as
                 the Android process has started. This theme is visible to the user
                 while the Flutter UI initializes. After that, this theme continues
                 to determine the Window background behind the Flutter UI. -->
            <meta-data
              android:name="io.flutter.embedding.android.NormalTheme"
              android:resource="@style/NormalTheme"
              />
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
        </activity>
        <!-- Don't delete the meta-data below.
             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java -->
        <meta-data
            android:name="flutterEmbedding"
            android:value="2" />

       <!-- V-KEY required  ## START ## -->
       <uses-library
           android:name="org.apache.http.legacy"
           android:required="false" />

       <service
           android:name="com.vkey.android.internal.vguard.cache.ProcessHttpRequestIntentService"
           android:permission="android.permission.BIND_JOB_SERVICE"
           android:exported="false"/>
       <service android:name="com.vkey.android.secure.overlay.OverlayService"
           android:exported="false"/>

       <service
           android:name="vkey.android.vos.MgService"
           android:enabled="true"
           android:process=":vkey"
           android:isolatedProcess="true"
           android:useAppZygote="true"/>
       <provider
           android:name="com.vkey.android.vguard.VGStartUpDetector"
           android:authorities="vkey.android.helloworld.start-up.detector"
           android:exported="false" />
       <!-- V-KEY required  ## END ## -->


       <meta-data
           android:name="firebase_messaging_auto_init_enabled"
           android:value="false" />
       <meta-data
           android:name="firebase_analytics_collection_enabled"
           android:value="false" />
    </application>
</manifest>
