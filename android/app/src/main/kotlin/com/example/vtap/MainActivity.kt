package com.example.vtap


import android.os.Bundle
import android.util.Log
import android.widget.Toast
import com.google.android.gms.common.GoogleApiAvailability
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugins.GeneratedPluginRegistrant


class MainActivity: FlutterActivity() {
    private val tag = "MainActivity"
    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)
        GeneratedPluginRegistrant.registerWith(flutterEngine)
        val vguardPlugin = VkVguardPlugin.getInstance()
        vguardPlugin.initChannels(flutterEngine)
    }



    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        checkGooglePlayServices()
    }

    private fun checkGooglePlayServices() {
        GoogleApiAvailability.getInstance()
            .makeGooglePlayServicesAvailable(this)
            .addOnSuccessListener { Log.d(tag, "makeGooglePlayServicesAvailable().onSuccess()") }
            .addOnFailureListener(this) { e ->
                Log.d(tag, "makeGooglePlayServicesAvailable().onFailure()")
                e.printStackTrace()
                Toast.makeText(
                    this@MainActivity,
                    "Google Play services upgrade required",
                    Toast.LENGTH_SHORT
                ).show()

                // can't proceed without GPS; quit
                <EMAIL>() // this causes a crash
            }
    }
}

