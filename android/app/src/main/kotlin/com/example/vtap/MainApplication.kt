package com.example.vtap

import android.app.Activity
import android.app.Application
import android.content.Context
import android.os.Bundle
import android.util.Log
import io.flutter.app.FlutterApplication

class MainApplication: FlutterApplication(), Application.ActivityLifecycleCallbacks {
    private val tag = "MainApplication"
    private var vguardPlugin:VkVguardPlugin? = null
    override fun onCreate() {
        super.onCreate()
        appContext = this
        vguardPlugin = VkVguardPlugin.getInstance()
        registerActivityLifecycleCallbacks(this)
    }

    companion object {
        var appContext: Context? = null
    }
    private var _currActivity = 0
    override fun onActivityCreated(activity: Activity, savedInstanceState: Bundle?) {
        _currActivity++
        Log.d(tag, "onActivityCreated: $_currActivity")
        if (vguardPlugin?.vGuardMgr == null && activity is MainActivity) {
            vguardPlugin?.registerVguardReceivers(activity)
            vguardPlugin?.onCreate(activity)
        }
    }

    override fun onActivityStarted(activity: Activity) {
        
    }

    override fun onActivityResumed(activity: Activity) {
        vguardPlugin?.onResume(activity)
    }

    override fun onActivityPaused(activity: Activity) {
        vguardPlugin?.onPause()
    }

    override fun onActivityStopped(activity: Activity) {

    }

    override fun onActivitySaveInstanceState(activity: Activity, outState: Bundle) {
        
    }

    override fun onActivityDestroyed(activity: Activity) {
        _currActivity--
        Log.d(tag, "onActivityDestroyed: $_currActivity")
        if (activity is MainActivity) {
            vguardPlugin?.unregisterVguardReceivers(activity)
            vguardPlugin?.onDestroy()
        }
    }


}