package com.example.vtap

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Parcelable
import android.text.TextUtils
import android.util.Log
import com.vkey.android.internal.vguard.engine.BasicThreatInfo
import com.vkey.android.vguard.*
import com.vkey.android.vguard.model.*
import com.vkey.securefileio.*
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.EventChannel
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import org.json.JSONArray
import org.json.JSONObject
import vkey.android.vos.VosWrapper
import java.io.File
import java.io.IOException
import java.lang.ref.WeakReference
import java.util.*

class VkVguardPlugin : MethodChannel.MethodCallHandler, EventChannel.StreamHandler, VGExceptionHandler {
    companion object {
        private var sInstance: VkVguardPlugin? = null

        fun getInstance(): VkVguardPlugin {
            if (sInstance == null) {
                sInstance = VkVguardPlugin()
            }
            return sInstance ?: throw IllegalStateException("")
        }
    }

    private val TAG = "VguardPlugin"
    private val vguardpluginChannel = "com.vkey.vguard/vguardplugin"
    private val vguardReceiverChannel = "com.vkey.vguard/vguardreceiver"

    private lateinit var channel: MethodChannel
    private var messageChannel: EventChannel? = null
    private var eventSink: EventChannel.EventSink? = null
    private var currentActivity : WeakReference<Activity>? = null
    var arrayThreat = mutableListOf<Map<String,String>>()
    private val VOS_TRUSTED_STORAGE_ERROR_CODE: List<String> = mutableListOf("-3", "-5", "20035")
    private val CRITICAL_ERROR_CODE: List<String> =
        mutableListOf("20030", "20031", "20032", "20033")
    private val EMULATOR_ERROR_CODE: List<String> = mutableListOf("-1039", "20050")
    private var highestThreatPolicy: VGThreatPolicy? = null

    fun initChannels(flutterEngine: FlutterEngine) {
        // TODO: setup V-OS App Protection here,
        // see steps that follow
        channel = MethodChannel(flutterEngine.dartExecutor.binaryMessenger, vguardpluginChannel)
        channel.setMethodCallHandler(this)

        messageChannel = EventChannel(flutterEngine.dartExecutor.binaryMessenger, vguardReceiverChannel)
        messageChannel?.setStreamHandler(this)
    }

    // Event Channel
    override fun onListen(arguments: Any?, eventSink: EventChannel.EventSink?) {
        this.eventSink = eventSink
    }

    override fun onCancel(arguments: Any?) {
        eventSink = null
        messageChannel = null
    }

    // Method Channel
    override fun onMethodCall(call: MethodCall, result: MethodChannel.Result) {
        when (call.method) {
            "setupVGuard" -> {
                setupVGuard(MainApplication.appContext)
            }
            "requestScan" -> {
                resetThreatList()
                val vguard: VGuard = VGuardFactory.getInstance()
                vguard.requestScan()
            }
            "sdkVersion" -> {
                val vguard: VGuard  = VGuardFactory.getInstance()
                val version = vguard.sdkVersion()
                result.success(version)
            }
            "resetVOSTrustedStorage" -> {
                val vguard: VGuard  = VGuardFactory.getInstance()
                val rs = vguard.resetVOSTrustedStorage()
                result.success(rs)
            }
            "allowsArbitraryNetworking" -> {
                val vguard: VGuard  = VGuardFactory.getInstance()
                val allow: Boolean? = call.argument("allow")
                if(allow != null) {
                    vguard.allowsArbitraryNetworking(allow)
                }
            }
            "setLoggerBaseUrl" -> {
                val tlaUrl: String? = call.argument("url")
                 VosWrapper.getInstance(MainApplication.appContext).setLoggerBaseUrl(tlaUrl)
            }
            "forceSyncLogs" -> {
                 VosWrapper.getInstance(MainApplication.appContext).forceSyncLogs()
            }
            "decryptString" -> {
                val filename: String? = call.argument("filename")
                val password: String = call.argument("password")?:""
                if(filename != null) {
                    try {
                        val rs = _decryptString(filename, password)
                        result.success(rs)
                    }
                    catch (e: Exception) {
                        result.error("Error", e.message?:e.toString(), null)
                    }
                }
                else {
                    result.error("Error", "MissingParam: filename" , null)
                }
            }
            "encryptString" -> {
                val str: String? = call.argument("str")
                val filename: String? = call.argument("filename")
                val password: String = call.argument("password")?:""
                val atomically: Boolean = call.argument("atomically")?:true
                if(str != null && filename != null) {
                    try {
                        _encryptString(str, filename, password, atomically)
                        result.success(true)
                    }
                    catch (e: Exception) {
                        result.error("Error", e.message?:e.toString(), null)
                    }
                }
                else {
                    result.error("Error", "MissingParam: filename" , null)
                }
            }
            else -> {
                result.notImplemented()
            }
        }
    }


    fun sendVguardEvent(eventName:String, data:Any?) {
        Log.i(TAG, "name: $eventName")
        Log.i(TAG, "data: $data")
        eventSink?.success(mutableMapOf("name" to eventName, "data" to data))
    }

    @Throws(IOException::class)
    fun _decryptString(filename: String, password: String): String {
        val file = File(MainApplication.appContext?.filesDir, filename)
        return SecureFileIO.decryptString(file.absolutePath, password)
    }

    @Throws(IOException::class)
    fun _encryptString(str:String , filename:String, password: String, atomically: Boolean){
        val file = File(MainApplication.appContext?.filesDir, filename)
        SecureFileIO.encryptString(str, file.absolutePath, password, atomically)
    }
    /*** Init VKey SDK ----- Copy start ----- **/

    private var isVosReady = false

    private val VGUARD_EXCEPTION = "vguardException"
    var vGuardMgr: VGuard? = null
    // LifecycleHook to notify VGuard of activity's lifecycle
    private var hook: VGuardLifecycleHook? = null
    // For VGuard to notify host app of events
    private var broadcastRvcr: VGuardBroadcastReceiver? = null
    private var resetVOSTrustedStorageRvcr: VGuardBroadcastReceiver? = null

     private fun setupVGuard(context: Context?) {
        Log.d(TAG, "setupVGuard called 1")
        if (vGuardMgr != null) return
        try {
            Log.d(TAG, "setupVGuard called")
            //Enable overlay detection
            val featureToggleManager = FeatureToggleManager.getInstance()
            featureToggleManager.enableGenericFeature(FeatureToggleManager.FeatureName.OVERLAY_DETECTION, true)
            //Setup Vguard
            val builder: VGuardFactory.Builder = VGuardFactory.Builder()
                .setAllowsArbitraryNetworking(false)
                .setDebugable(false)
                .setMemoryConfiguration(MemoryConfiguration.HIGH)
                .setVGExceptionHandler(this)
            //Enable Virtual Tap Detection
                .setVirtualTapDetectionEnabled(true)
            VGuardFactory().getVGuard(context, builder)
        } catch (e: Exception) {
            sendVguardEvent(VGUARD_EXCEPTION, "A serious exception has occurred within V-Guard: " + e.message)
        }
    }

    override fun handleException(exception: Exception?) {
        getVGuardInstance()
        sendVguardEvent(VGUARD_EXCEPTION, exception?.message)
        val errorCode = exception?.message
        if(errorCode != null) {
            if(EMULATOR_ERROR_CODE.contains(errorCode)) {
                Log.e(TAG, "Emulator detected!")
                sendVguardEvent(VGUARD_EXCEPTION, "Emulator detected!")
            }
            else {
                //Check critical error
                if(CRITICAL_ERROR_CODE.contains(errorCode)) {
                    Log.e(TAG,"Critical error happened!")
                    sendVguardEvent(VGUARD_EXCEPTION, "Critical error happened!")
                }
                else {
                    //Check trusted storage error
                    if(VOS_TRUSTED_STORAGE_ERROR_CODE.contains(errorCode)) {
                        Log.e(TAG,"Trusted storage error happened!")
                        vGuardMgr!!.resetVOSTrustedStorage()
                        vGuardMgr!!.destroy()
                        setupVGuard(currentActivity?.get())
                    }
                    else {
                        try{
                            val error = Integer.parseInt(errorCode)
                            //20000 <= error < 30000 (V-guard errors)
                            if(error in 20000..29999) {
                                Log.e(TAG,"Vguard error happened!")
                                sendVguardEvent(VGUARD_EXCEPTION, "Vguard error happened!")
                            } else {
                                Log.e(TAG,"Vos error happened!")
                                sendVguardEvent(VGUARD_EXCEPTION, "Vos error happened!")
                            }
                        } catch (ex: Exception) {
                            Log.e(TAG, ex.message.toString())
                        }
                    }
                }
            }
        }
    }

    private fun getVGuardInstance() {
        if (vGuardMgr == null) {
            vGuardMgr = VGuardFactory.getInstance() as VGuard
            Log.i(TAG, "vGuardMgr: getVGuardInstance")
        }
        // necessary for VGuard to be informed of the activity's lifecycle
        hook = ActivityLifecycleHook(vGuardMgr)
    }
    fun onCreate(activity: Activity) {
        currentActivity = WeakReference<Activity>(activity)
    }
    fun onResume(activity: Activity) {
        currentActivity = WeakReference<Activity>(activity)
        if (vGuardMgr != null) {
            try {
                vGuardMgr!!.onResume(hook, activity)
            } catch (e: java.lang.Exception) {
                Log.e(TAG,"vGuardMgr.onPause throw exception causes: " + e.message)
            }
        }
    }

    fun onPause() {
        if (vGuardMgr != null) {
            try {
                vGuardMgr!!.onPause(hook)
            } catch (e: java.lang.Exception) {
                Log.e(TAG,"vGuardMgr.onPause throw exception causes: " + e.message)
            }
        }
    }

    private fun resetThreatList() {
        arrayThreat = mutableListOf()
    }

    fun onDestroy() {
        Log.d(TAG, "destroying vGuardMgr")
        resetThreatList()
        if (vGuardMgr != null) {
            try {
                vGuardMgr!!.destroy()
            } catch (e: java.lang.Exception) {
                Log.e(TAG,"vGuardMgr.onDestroy throw exception causes: " + e.message)
            }
            vGuardMgr = null
        }
        currentActivity = null
    }



     fun unregisterVguardReceivers(activity: Activity) {
         val localBroadcastManager = LocalBroadcastManager.getInstance(activity)
        if (broadcastRvcr != null) {
            localBroadcastManager.unregisterReceiver(broadcastRvcr)
        }
        if(resetVOSTrustedStorageRvcr != null) {
            localBroadcastManager.unregisterReceiver(resetVOSTrustedStorageRvcr)
        }
    }

    fun registerVguardReceivers(activity: Activity) {
        // for receiving notifications from VGuard
        broadcastRvcr = object : VGuardBroadcastReceiver(null) {
            override fun onReceive(context: Context, intent: Intent) {
                super.onReceive(context, intent)
                when(intent.action){
                    ACTION_FINISH -> {
                        sendVguardEvent("ACTION_FINISH", null)
                    }
                    VOS_READY -> {
                        getVGuardInstance()
                        val tIUrl = "" // ""https://..."
                        if(!TextUtils.isEmpty(tIUrl) && vGuardMgr != null) {
                            vGuardMgr?.setThreatIntelligenceServerURL(tIUrl)
                        }
                        val VOS_FIRMWARE_RETURN_CODE_KEY = "vkey.android.vguard.FIRMWARE_RETURN_CODE"
                        val firmwareReturnCode = intent.getLongExtra(VOS_FIRMWARE_RETURN_CODE_KEY, 0)
                        Log.d(TAG, "\nv-os return code: $firmwareReturnCode")
                        isVosReady = (firmwareReturnCode > 0)
                        sendVguardEvent("VOS_READY", firmwareReturnCode)
                    }
                    ACTION_SCAN_COMPLETE -> {
                        val arrayData = getArrayThreats(intent)
                        arrayThreat.addAll(arrayData)
                        if (intent.hasExtra(SCAN_COMPLETE_LEVEL)) {
                            val scanLevel =
                                intent.getSerializableExtra(SCAN_COMPLETE_LEVEL) as VGScanLevel?
                            //If you are using TI, scanning process will be completed when you receive TI_COMPLETED.
                            //If you are not using TI, scanning process will be completed when you receive L3_COMPLETED.
                            if (scanLevel == VGScanLevel.L3_COMPLETED) {
                                sendVguardEvent("ACTION_SCAN_COMPLETE", arrayThreat)
                            }
                        }
                    }
                    VGUARD_OVERLAY_DETECTED -> {
                        sendVguardEvent("VGUARD_OVERLAY_DETECTED", true)
                    }
                    VGUARD_OVERLAY_DETECTED_DISABLE -> {
                        sendVguardEvent("VGUARD_OVERLAY_DETECTED_DISABLE", true)
                    }
                    VGUARD_STOP_OVERLAY_SERVICE -> {
                        sendVguardEvent("VGUARD_STOP_OVERLAY_SERVICE", true)
                    }
                    VGUARD_VIRTUAL_SPACE_DETECTED -> {
                        sendVguardEvent("VGUARD_VIRTUAL_SPACE_DETECTED", true)
                        val virtualType =
                            intent.getSerializableExtra(VGUARD_VIRTUAL_SPACE_TYPE) as VGVirtualSpaceType
                        when(virtualType){
                            VGVirtualSpaceType.APP_BASED -> {
                                sendVguardEvent("VGVirtualSpaceType", "APP_BASED")
                            }
                            VGVirtualSpaceType.SYSTEM_BASED -> {
                                sendVguardEvent("VGVirtualSpaceType", "SYSTEM_BASED")
                            }
                            else -> {
                                sendVguardEvent("VGVirtualSpaceType", "NONE")
                            }
                        }
                    }
                    VGUARD_SCREEN_SHARING_DETECTED -> {
                        Log.d(TAG, "\n\nScreen Sharing Detected")
                        val sharingDisplays = intent.getStringExtra(VGUARD_SCREEN_SHARING_DISPLAY_NAMES)
                        val jsonArray = JSONArray(sharingDisplays)
                        sendVguardEvent("VGUARD_VIRTUAL_SPACE_DETECTED", jsonArray.toString())
                    }
                    VGUARD_SIDELOADED_APP_WITH_ACCESSIBILITY_PERMISSION_DETECTED -> {
                        try {
                            val sideloadlist = intent.getStringExtra(VGUARD_SIDELOADED_RESULT)
                            if (!TextUtils.isEmpty(sideloadlist)) {
                                sendVguardEvent("VGUARD_SIDELOADED_APP_WITH_ACCESSIBILITY_PERMISSION_DETECTED", sideloadlist)
                            } else {
                                val builder = StringBuilder()
                                val packageID =
                                    intent.getStringExtra("vkey.android.vguard.VGUARD_SIDELOADED_PACKAGE_ID")
                                val source =
                                    intent.getStringExtra("vkey.android.vguard.VGUARD_SIDELOADED_SOURCE")
                                builder.append("\nPackageID: $packageID")
                                builder.append("\nSource Install: $source")
                            }
                        } catch (e: Exception) {
                            e.printStackTrace()
                        }
                    }
                    VGUARD_STATUS -> {
                        Log.d(TAG, "\nVGuard status ... ")

                        if (intent.hasExtra(VGUARD_HANDLE_THREAT_POLICY)) {
                            // // If the profile that you use has the vguardHandleThreatPolicy set to false
                            handleThreatPolicy(intent, "VGUARD_HANDLE_THREAT_POLICY")
                        }
                        else if (intent.hasExtra(VGUARD_SSL_ERROR_DETECTED)) {
                            // If the profile that you use has the sslAlertBypass set to true,
                            handleSslErrorDetection(intent, "VGUARD_SSL_ERROR_DETECTED")
                        }
                        else {
                            val message = intent.getStringExtra(VGUARD_MESSAGE)
                            Log.d(TAG, "VGUARD_MESSAGE: $message")
                            /*if(message != null) {
                                sendVguardEvent("VGUARD_STATUS", message)
                            }*/
                        }
                    }
                    VGUARD_DEVELOPER_OPTIONS_ENABLED -> {
                        sendVguardEvent("VGUARD_DEVELOPER_OPTIONS_ENABLED", true)
                    }
                    VGUARD_NETWORK_DETECTED -> {
                        sendVguardEvent("VGUARD_NETWORK_DETECTED", true)
                        val networkType = intent.getSerializableExtra(VGUARD_NETWORK_TYPES) as Array<*>
                        val arrayNetworkType = getArrayNetworkTypes(networkType)
                        for(type in arrayNetworkType) {
                            sendVguardEvent("VGUARD_NETWORK_TYPE", type)
                        }
                    }
                    VGUARD_VIRTUAL_TAP_DETECTED -> {
                        sendVguardEvent("VGUARD_VIRTUAL_TAP_DETECTED", true)
                        val type = intent.getSerializableExtra(VGUARD_VIRTUAL_TAP_TYPE) as VGVirtualTapType
                        val virtualTapTypeString = convertVirtualTapTypeToString(type)
                        sendVguardEvent("VGUARD_VIRTUAL_TAP_TYPE", virtualTapTypeString)
                    }
                    VGUARD_AIRDROID_PORT_IS_OPEN -> {
                        sendVguardEvent("VGUARD_AIRDROID_PORT_IS_OPEN", true)
                    }
                }
            }
        }

        resetVOSTrustedStorageRvcr = object : VGuardBroadcastReceiver(null) {
            override fun onReceive(context: Context, intent: Intent) {
                super.onReceive(context, intent)
                val isResetVOSTrustedStorageSuccess = vGuardMgr!!.resetVOSTrustedStorage()
                sendVguardEvent("RESET_VOS_STORAGE", isResetVOSTrustedStorageSuccess)
            }
        }

        // register using LocalBroadcastManager only for keeping data within your app
        val localBroadcastMgr = LocalBroadcastManager.getInstance(activity)

        // necessary for vguard to finish activity safely
        localBroadcastMgr.registerReceiver(broadcastRvcr, IntentFilter(VGuardBroadcastReceiver.ACTION_FINISH))
        localBroadcastMgr.registerReceiver(broadcastRvcr, IntentFilter(VGuardBroadcastReceiver.ACTION_SCAN_COMPLETE))
        localBroadcastMgr.registerReceiver(broadcastRvcr, IntentFilter(VGuardBroadcastReceiver.VOS_READY))
        localBroadcastMgr.registerReceiver(broadcastRvcr, IntentFilter(VGuardBroadcastReceiver.VGUARD_OVERLAY_DETECTED))
        localBroadcastMgr.registerReceiver(broadcastRvcr, IntentFilter(VGuardBroadcastReceiver.VGUARD_STOP_OVERLAY_SERVICE))
        localBroadcastMgr.registerReceiver(broadcastRvcr, IntentFilter(VGuardBroadcastReceiver.VGUARD_OVERLAY_DETECTED_DISABLE))
        localBroadcastMgr.registerReceiver(broadcastRvcr, IntentFilter(VGuardBroadcastReceiver.VGUARD_VIRTUAL_SPACE_DETECTED))
        localBroadcastMgr.registerReceiver(broadcastRvcr, IntentFilter(VGuardBroadcastReceiver.VGUARD_SCREEN_SHARING_DETECTED))
        localBroadcastMgr.registerReceiver(broadcastRvcr, IntentFilter(VGuardBroadcastReceiver.VGUARD_SIDELOADED_APP_WITH_ACCESSIBILITY_PERMISSION_DETECTED))
        localBroadcastMgr.registerReceiver(broadcastRvcr, IntentFilter(VGuardBroadcastReceiver.VGUARD_STATUS))
        localBroadcastMgr.registerReceiver(broadcastRvcr, IntentFilter(VGuardBroadcastReceiver.VGUARD_DEVELOPER_OPTIONS_ENABLED))
        localBroadcastMgr.registerReceiver(broadcastRvcr, IntentFilter(VGuardBroadcastReceiver.VGUARD_NETWORK_DETECTED))
        localBroadcastMgr.registerReceiver(broadcastRvcr, IntentFilter(VGuardBroadcastReceiver.VGUARD_VIRTUAL_TAP_DETECTED))
        localBroadcastMgr.registerReceiver(broadcastRvcr, IntentFilter(VGuardBroadcastReceiver.VGUARD_AIRDROID_PORT_IS_OPEN))

        val RESET_VOS_STORAGE = "vkey.android.vguard.resetVOSTrustedStorageRvcr"
        localBroadcastMgr.registerReceiver(resetVOSTrustedStorageRvcr, IntentFilter(RESET_VOS_STORAGE))
    }

    private fun getArrayNetworkTypes(networkTypes: Array<*>): ArrayList<String> {
        val arrayData: ArrayList<String> = ArrayList()
        for (networkType in networkTypes) {
            val type = networkType as VGuardNetworkType
            val networkTypeString = convertNetworkTypeToString(type)
            arrayData.add(networkTypeString)
        }
        return arrayData
    }

    private fun convertVirtualTapTypeToString(type: VGVirtualTapType): String {
        return when (type) {
            VGVirtualTapType.VIRTUAL_TAP -> "VIRTUAL_TAP"
            VGVirtualTapType.CORELLIUM_VD -> "CORELLIUM_VD"
            VGVirtualTapType.ANDROID_STUDIO_EMULATOR -> "ANDROID_STUDIO_EMULATOR"
            VGVirtualTapType.AIRDROID_USB_DEBUG -> "AIRDROID_USB_DEBUG"
            VGVirtualTapType.P_CLOUDY_NON_VIRTUAL -> "P_CLOUDY_NON_VIRTUAL"
            else -> "UNKNOWN"
        }
    }

    private fun convertNetworkTypeToString(networkType: VGuardNetworkType): String {
        return when (networkType) {
            VGuardNetworkType.WIFI -> "WIFI"
            VGuardNetworkType.CELLULAR -> "CELLULAR"
            VGuardNetworkType.VPN -> "VPN"
            VGuardNetworkType.ETHERNET -> "ETHERNET"
            VGuardNetworkType.BLUETOOTH -> "BLUETOOTH"
            VGuardNetworkType.WIFI_AWARE -> "WIFI_AWARE"
            VGuardNetworkType.LOWPAN -> "LOWPAN"
            VGuardNetworkType.USB -> "USB"
            VGuardNetworkType.THREAD -> "THREAD"
            VGuardNetworkType.SATELLITE -> "SATELLITE"
            else -> "UNKNOWN"
        }
    }

    private fun getArrayThreats(intent: Intent): MutableList<Map<String,String>> {
        val detectedThreats =
            intent.getParcelableArrayListExtra<Parcelable>(VGuardBroadcastReceiver.SCAN_COMPLETE_RESULT) as ArrayList<Parcelable>?
        val builder = java.lang.StringBuilder()
        val arrayData = mutableListOf<Map<String,String>>()
        for (info in detectedThreats!!) {
            val threatInfo = info as BasicThreatInfo
            val infoMap = mutableMapOf<String, String>()
            infoMap["ThreatClass"] =  threatInfo.threatClass
            infoMap["ThreatInfo"] =  threatInfo.threatInfo
            infoMap["ThreatName"] =  threatInfo.threatName
            // infoMap["ThreatPackageID"] =  threatInfo.threatPackage
            arrayData.add(infoMap)
            // print log
            val infoStr = info.toString()
            builder.append(infoStr).append("\n")
        }
        Log.d(TAG, "\n\nDetected Threats: $builder")
        return arrayData
    }

    private fun getArrayResponses(intent: Intent): MutableList<Map<String,String>> {
        val threatResponses =
            intent.getParcelableArrayListExtra<Parcelable>(VGuardBroadcastReceiver.SCAN_COMPLETE_RESULT) as ArrayList<Parcelable>?
        val builder = java.lang.StringBuilder()
        val arrayData = mutableListOf<Map<String,String>>()
        for (response in threatResponses!!) {
            val threatResponse = response as VGThreatResponse
            val infoMap = mutableMapOf<String, String>()
            infoMap["title"] =  threatResponse.title
            infoMap["message"] =  threatResponse.message
            infoMap["categoryName"] =  threatResponse.categoryName
            infoMap["categoryValue"] = threatResponse.categoryValue
            infoMap["threatPolicy"] = convertThreatPolicyToString(threatResponse.threatPolicy)
            if(highestThreatPolicy == null) {
                highestThreatPolicy = response.threatPolicy
            }
            var allThreat = ""
            for(threat in threatResponse.threats) {
                allThreat += threat.toString() +"\n"
            }
            infoMap["threats"] = allThreat
            arrayData.add(infoMap)
            // print log
            val infoStr = threatResponse.toString()
            builder.append(infoStr).append("\n")
        }
        Log.d(TAG, "\n\nDetected Threats: $builder")
        return arrayData
    }

    private fun convertThreatPolicyToString(highestResponse: VGThreatPolicy?): String {
        return if (highestResponse != null) {
            when (highestResponse) {
                VGThreatPolicy.BY_PASS -> {
                    "BY_PASS"
                }

                VGThreatPolicy.ALERT_USER -> {
                    "ALERT_USER"
                }

                VGThreatPolicy.QUIT_APP -> {
                    "QUIT_APP"
                }

                VGThreatPolicy.DISABLE_APP -> {
                    "DISABLE_APP"
                }

                VGThreatPolicy.BLOCK_NETWORK -> {
                    "BLOCK_NETWORK"
                }
            }
        } else ""
    }

    private fun handleSslErrorDetection(intent: Intent, eventName: String) {
        val sslErr = intent.getBooleanExtra(VGuardBroadcastReceiver.VGUARD_SSL_ERROR_DETECTED, false)
        Log.i(TAG, "${VGuardBroadcastReceiver.VGUARD_SSL_ERROR_DETECTED}:$sslErr".trimIndent())
        val mapData = mutableMapOf<String, Any>()
        mapData["VGUARD_SSL_ERROR_DETECTED"] = sslErr
        if (sslErr) {
            try {
                val jsonObject = JSONObject(intent.getStringExtra(VGuardBroadcastReceiver.VGUARD_MESSAGE)
                    .toString())
                mapData[VGuardBroadcastReceiver.VGUARD_ALERT_TITLE] =  jsonObject.optString(VGuardBroadcastReceiver.VGUARD_ALERT_TITLE)
                mapData[VGuardBroadcastReceiver.VGUARD_ALERT_MESSAGE] =  jsonObject.optString(VGuardBroadcastReceiver.VGUARD_ALERT_MESSAGE)
                Log.i(TAG, jsonObject.toString())
            } catch (e: java.lang.Exception) {
                e.printStackTrace()
            }
        }
        sendVguardEvent(eventName, mapData)
    }

    private fun handleThreatPolicy(intent: Intent, eventName: String) {
        val mapData = mutableMapOf<String, Any?>()
        val arrayResponses = getArrayResponses(intent)
        mapData["responses"] =  arrayResponses
        val highestResponse: VGThreatPolicy?
        if(intent.hasExtra(VGuardBroadcastReceiver.VGUARD_HIGHEST_THREAT_POLICY)) {
            highestResponse = intent.getSerializableExtra(VGuardBroadcastReceiver.VGUARD_HIGHEST_THREAT_POLICY) as VGThreatPolicy
        } else {
            highestResponse = highestThreatPolicy
            highestThreatPolicy = null
        }
        mapData[VGuardBroadcastReceiver.VGUARD_HIGHEST_THREAT_POLICY] = convertThreatPolicyToString(highestResponse)
        val alertTitle = ""
        if(intent.hasExtra(VGuardBroadcastReceiver.VGUARD_ALERT_TITLE)) {
            intent.getStringExtra(VGuardBroadcastReceiver.VGUARD_ALERT_TITLE)
            mapData[VGuardBroadcastReceiver.VGUARD_ALERT_TITLE] = alertTitle
        }
        val alertMsg = ""
        if(intent.hasExtra(VGuardBroadcastReceiver.VGUARD_ALERT_MESSAGE)) {
            intent.getStringExtra(VGuardBroadcastReceiver.VGUARD_ALERT_MESSAGE)
            mapData[VGuardBroadcastReceiver.VGUARD_ALERT_MESSAGE] = alertMsg
        }
        val disabledAppExpired = intent.getLongExtra(VGuardBroadcastReceiver.VGUARD_DISABLED_APP_EXPIRED, 0)
        mapData[VGuardBroadcastReceiver.VGUARD_DISABLED_APP_EXPIRED] = disabledAppExpired

        Log.i(TAG, "Vguard Status datas: $mapData")
        sendVguardEvent(eventName, mapData)
    }


}