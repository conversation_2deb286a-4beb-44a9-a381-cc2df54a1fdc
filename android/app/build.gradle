plugins {
    id "com.android.application"
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
}
def localProperties = new Properties()

def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}
android {
    namespace 'com.example.vtap'
    compileSdk 34

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    useLibrary 'org.apache.http.legacy'

    defaultConfig {
        applicationId "vkey.android.helloworld"
        minSdkVersion 21
        targetSdkVersion 34
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName

        ndk {
            abiFilters 'x86', 'armeabi-v7a', 'arm64-v8a', 'x86_64'
        }
        multiDexEnabled true
    }
    signingConfigs {
        config {
            keyAlias 'VTapKey'
            keyPassword 'vtapkey'
            storeFile file('VosTestApp.jks')
            storePassword 'vtapkey'
            v1SigningEnabled true
            v2SigningEnabled true
        }
    }
    buildTypes {
        release {
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.config
        }
        debug {
            debuggable true
            jniDebuggable true
            minifyEnabled false
            // proguardFiles getDefault  ProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.config
        }
    }
    buildFeatures {
        viewBinding true
    }
}

flutter {
    source '../..'
}

dependencies {
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.7.10"

    implementation 'com.google.code.gson:gson:2.8.2'
    api 'io.jsonwebtoken:jjwt-api:0.10.7'
    runtimeOnly 'io.jsonwebtoken:jjwt-impl:0.10.7'
    runtimeOnly('io.jsonwebtoken:jjwt-orgjson:0.10.7') {
        exclude group: 'org.json', module: 'json' //provided by Android natively
    }
    implementation 'com.google.android.gms:play-services-base:18.0.1'
    implementation 'com.getkeepsafe.relinker:relinker:1.4.4'
    implementation "androidx.security:security-crypto:1.0.0"
    implementation 'com.google.android.material:material:1.4.0'

    implementation(files("libs/vos-processor-android-4.10.2.0-Debug.aar"))
    implementation(files("libs/vos-app-protection-android-4.10.2.5-Debug.aar"))
    implementation files('libs/securefileio-android-4.7.7.8-Debug.aar')

//    implementation 'com.amplifyframework:rxbindings:1.37.3'
//    implementation 'com.amplifyframework:aws-analytics-pinpoint:1.37.2'
//    implementation 'com.amplifyframework:aws-api:1.37.2'
//    implementation 'com.amplifyframework:aws-auth-cognito:1.37.2'
//    implementation 'com.amplifyframework:aws-datastore:1.37.2'
//    implementation 'com.amplifyframework:aws-predictions:1.37.2'
//    implementation 'com.amplifyframework:aws-storage-s3:1.37.2'
}
