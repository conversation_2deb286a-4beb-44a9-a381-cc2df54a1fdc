{"inputs": ["/Users/<USER>/Documents/On-going/Flutter/Vguard/vguard-multiplatform-sample-SDK410/.dart_tool/flutter_build/05560860b0c18d9fd5ad9b252289ae2d/App.framework/App", "/Users/<USER>/Documents/On-going/Flutter/Vguard/vguard-multiplatform-sample-SDK410/pubspec.yaml", "/Users/<USER>/development/flutter/packages/flutter_tools/lib/src/build_system/targets/icon_tree_shaker.dart", "/Users/<USER>/development/flutter/bin/internal/engine.version", "/Users/<USER>/development/flutter/bin/internal/engine.version", "/Users/<USER>/development/flutter/packages/flutter_tools/lib/src/build_system/tools/shader_compiler.dart", "/Users/<USER>/development/flutter/bin/internal/engine.version", "/Users/<USER>/Documents/On-going/Flutter/Vguard/vguard-multiplatform-sample-SDK410/.dart_tool/flutter_build/05560860b0c18d9fd5ad9b252289ae2d/App.framework.dSYM/Contents/Resources/DWARF/App", "/Users/<USER>/Documents/On-going/Flutter/Vguard/vguard-multiplatform-sample-SDK410/pubspec.yaml", "/Users/<USER>/Documents/On-going/Flutter/Vguard/vguard-multiplatform-sample-SDK410/ios/Runner/Info.plist", "/Users/<USER>/Documents/On-going/Flutter/Vguard/vguard-multiplatform-sample-SDK410/ios/Flutter/AppFrameworkInfo.plist", "/Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.5/assets/CupertinoIcons.ttf", "/Users/<USER>/development/flutter/bin/cache/artifacts/material_fonts/MaterialIcons-Regular.otf", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/shaders/ink_sparkle.frag", "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.11.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/boolean_selector-2.1.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.3.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.5/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/fake_async-1.3.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_easyloading-3.0.5/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_lints-2.0.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_spinkit-5.1.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/http-0.13.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.0.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker-10.0.5/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.5/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_testing-3.0.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/lints-2.0.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/matcher-0.12.16+1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.15.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/responsive_framework-0.2.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.11.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.2.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/test_api-0.7.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.3.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/uni_links-0.5.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/uni_links_platform_interface-1.0.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/uni_links_web-0.1.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.1.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.0.17/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.0.17/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.0.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.0.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.1.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_web-2.0.12/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.0.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/vm_service-14.2.5/LICENSE", "/Users/<USER>/development/flutter/bin/cache/pkg/sky_engine/LICENSE", "/Users/<USER>/development/flutter/packages/flutter/LICENSE"], "outputs": ["/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-ebybunlcvccxiqanqwotqbhzsjgz/Build/Products/Release-iphoneos/App.framework/App", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-ebybunlcvccxiqanqwotqbhzsjgz/Build/Products/Release-iphoneos/App.framework/Info.plist", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-ebybunlcvccxiqanqwotqbhzsjgz/Build/Products/Release-iphoneos/App.framework.dSYM/Contents/Resources/DWARF/App", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-ebybunlcvccxiqanqwotqbhzsjgz/Build/Products/Release-iphoneos/App.framework/flutter_assets/packages/cupertino_icons/assets/CupertinoIcons.ttf", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-ebybunlcvccxiqanqwotqbhzsjgz/Build/Products/Release-iphoneos/App.framework/flutter_assets/fonts/MaterialIcons-Regular.otf", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-ebybunlcvccxiqanqwotqbhzsjgz/Build/Products/Release-iphoneos/App.framework/flutter_assets/shaders/ink_sparkle.frag", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-ebybunlcvccxiqanqwotqbhzsjgz/Build/Products/Release-iphoneos/App.framework/flutter_assets/AssetManifest.json", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-ebybunlcvccxiqanqwotqbhzsjgz/Build/Products/Release-iphoneos/App.framework/flutter_assets/AssetManifest.bin", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-ebybunlcvccxiqanqwotqbhzsjgz/Build/Products/Release-iphoneos/App.framework/flutter_assets/FontManifest.json", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-ebybunlcvccxiqanqwotqbhzsjgz/Build/Products/Release-iphoneos/App.framework/flutter_assets/NOTICES.Z"]}