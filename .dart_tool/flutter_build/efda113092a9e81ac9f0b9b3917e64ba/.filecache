{"version": 2, "files": [{"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/refresh.dart", "hash": "65b13c835848186d2090e77073d3c7ff"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/dialog.dart", "hash": "b066cb536dada4801466a198ed505a8d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/radio.dart", "hash": "79c4abb58f097365e2627de32331bffe"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/constants.dart", "hash": "d10f9a1cfa41452535b1fa21db8e1c8d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/two_dimensional_viewport.dart", "hash": "b8e14be092255a62d690c39bef984338"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/animated_scroll_view.dart", "hash": "493bf1de83d600106fe27cb741bd2012"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/long_press.dart", "hash": "be262921a4471e495202bfe1871b610e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/browser_context_menu.dart", "hash": "6a35dac0f777e7dd228bde492c4089b2"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/platform.dart", "hash": "e654c239899a91dab05b11cf0121129f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/utilities.dart", "hash": "3f5e8feebce49c954d9c5ac1cda935c1"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/performance_overlay.dart", "hash": "9651186d44281a8caacff14d50734011"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/interactive_viewer.dart", "hash": "203611b95394d042fae8bef59a4ebe40"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/localizations.dart", "hash": "4f4fcae47233bec91490b2c694f410d3"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/_capabilities_io.dart", "hash": "faf4d014b3617ede3150f80eba25e3b4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/equality_map.dart", "hash": "700328ab0177ddfd9a003a8c15619c1a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/list_wheel_viewport.dart", "hash": "8965e1b5627c77d3978ae3d08e32f982"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/quad.dart", "hash": "739bb2e85022ddfb653590b93216942a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/color_scheme.dart", "hash": "8f094e8fb77987b0922b558b2fd22102"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/semantics/binding.dart", "hash": "31f32173a8983ae7bddd822a3e0e48d1"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/system_channels.dart", "hash": "6147de3bb8f9f335022d631c67c92536"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/flexible_space_bar.dart", "hash": "0a90eefcfcb46a3f293d18d1142328ec"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/_platform_io.dart", "hash": "bf6d84f8802d83e64fe83477c83752b4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/spell_check_suggestions_toolbar_layout_delegate.dart", "hash": "942fbfca7541358613467c640e1ca6cb"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/material_button.dart", "hash": "da9ecd9bf1968692f391966d2c3c193e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/boolean_selector-2.1.1/LICENSE", "hash": "83228a1ae32476770262d4ff2ac6f984"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/date.dart", "hash": "467e7592ed2562b6ebc43d62b1015271"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/platform_view.dart", "hash": "799a4ef2a4bf9f5a72c65bac4ecf23a4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/text.dart", "hash": "1bb32014080277421d4d679148d11bb0"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/animated_icons.dart", "hash": "8288239ccc449f5dec9f381298c92c1d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/functions.dart", "hash": "a3aa36a805436731699f39e6bf524087"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/velocity_tracker.dart", "hash": "3fa4c89a1c19c846cce6950ff665c20a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/grid_tile.dart", "hash": "b526e1fcb69f0ca9df233cd2fb6e69a9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/sliver_persistent_header.dart", "hash": "c03374f2c27987a9929d2128490ae921"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/performance_overlay.dart", "hash": "9b9a244b67b67b84170d3c3ee4ec2c73"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/capabilities.dart", "hash": "c845d2f5cf6caad03afdca9d2aa141e7"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/form.dart", "hash": "1438fbc7f0f731cf5164a211928a377e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/wrap.dart", "hash": "21262cfe42ea14c1c1b45867971224d7"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/assertions.dart", "hash": "f5dfb544b24fa84a9f504c5b8be1d838"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/animation/animation_controller.dart", "hash": "7ba47d8d884d71e42aeabcf62344873c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/list_tile.dart", "hash": "bd2a37206b73cbcb99406f0b1ac68a09"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/undo_history.dart", "hash": "932fb518b9857889d8182a4d9d78d2d9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/animated_switcher.dart", "hash": "53b46c9af9e99869b44e05cfefbd7d46"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/reorderable_list.dart", "hash": "bd943d36cd0bfe732ff0bcad174fbab7"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/preferred_size.dart", "hash": "d498388a21cc769d98cf664c575d4e04"}, {"path": "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-ebybunlcvccxiqanqwotqbhzsjgz/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/shaders/ink_sparkle.frag", "hash": "ca43ef06214aa07591b0b3d9f3a7195c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/frustum.dart", "hash": "d975e51852aa1802c81c738dcb4c348d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scroll_delegate.dart", "hash": "a0740954b44e7627eebd8a66325727c5"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/persistent_hash_map.dart", "hash": "ca2f231e73aa51c866ef096e66d46cf2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/quaternion.dart", "hash": "698a6fc4361dd42bae9034c9c2b6cf7b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/services.dart", "hash": "046141d90f3922d04cc8e19212de421c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/vector3.dart", "hash": "478e1071c9f577b6cabb8d72c36de077"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker-10.0.5/LICENSE", "hash": "f721b495d225cd93026aaeb2f6e41bcc"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/navigation_drawer.dart", "hash": "d1888a7dc676f48f137edbea755dd324"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/data/close_menu.g.dart", "hash": "dd134142f6edb06d6ad1ebc0d27fb524"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/clipboard.dart", "hash": "2a64735d53a1dd225670c23206f09e60"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/button_style.dart", "hash": "43a371c8c7e417fb0a445ee20360d434"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/mouse_tracker.dart", "hash": "824c7dfb7d95a905ec1ba5f0d88aaf20"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/navigation_toolbar.dart", "hash": "e0da92f4435a19c3c666cb8f88cf4502"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/search_bar_theme.dart", "hash": "5c0db57261dd10e5f760ac757f4fcd76"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/banner.dart", "hash": "7ade1fb93c1507812cb7e1f3023b3cd4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/paginated_data_table.dart", "hash": "86a6fc84462d9d59a64d1c32494e96a6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.0.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/unmodifiable_wrappers.dart", "hash": "4be4077b482b12a5ee202d859e8286df"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/pages.dart", "hash": "e50e0016353ca39f15e2e40e2221095e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scroll_notification_observer.dart", "hash": "329b723b2cea0443e5ec2ccfb31fbfb8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.11.1/LICENSE", "hash": "3c68a7c20b2296875f67e431093dd99e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/card_theme.dart", "hash": "46ffe5265ab96981a4304879f9999d5d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/responsive_framework-0.2.0/lib/responsive_grid.dart", "hash": "3dbc81a947b2919f9c72eb13a2278bdb"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/scrollbar_theme.dart", "hash": "ec8275100b9b1a1d880b8ddfe8100c9f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/notched_shapes.dart", "hash": "62c1ce5453fdd075196695637e258b97"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/sliver_tree.dart", "hash": "d6d7e025154dccf2e3d0b12eb073f93a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/expansion_panel.dart", "hash": "ada618567778b8fea2e8b455e9100a07"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/divider.dart", "hash": "7397ee35fbfd4feddf487df2023f0ffa"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/scheduler/debug.dart", "hash": "0b9b4bed20850a8e57b54642782a88b2"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scroll_controller.dart", "hash": "3455df89df3647c70f9d2586275523b9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/context_menu_button_item.dart", "hash": "34517b36f5fc8d574ff2ffaadcd2b9a0"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/size_changed_layout_notifier.dart", "hash": "5528b93def00b5b750c964a10f323900"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/animated_cross_fade.dart", "hash": "629b25992db59361e5889a42c7de583c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/input_border.dart", "hash": "ffe5391f3b53b542cdd817dcd85e2638"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/binding.dart", "hash": "6c5adddb22296bd8e3a6b50a3a0ffbbe"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/animated_size.dart", "hash": "382f7c8ee5e19580005898427950e0b7"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/dropdown.dart", "hash": "5711ec38f26bd1c2412a9a943a40d4fb"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/desktop_text_selection.dart", "hash": "b87bce461399faa5b57c569a2fbfdc0e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/image_filter.dart", "hash": "e7651e730f1ce3e0a0b87ac950fcce68"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/lookup_boundary.dart", "hash": "0a3c66e5de5f99b50a256aac5e4207e6"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/animation/animation_style.dart", "hash": "d91b7655dec73b3db3a82040be7410f4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/flutter_logo.dart", "hash": "96bce067da5564de27f19f050360860b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/spacer.dart", "hash": "31caf5d9d4f0d5e2b373a2bf368290d6"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/texture.dart", "hash": "3c7543874ccaad16712efd4e0249db70"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/temperature/temperature_cache.dart", "hash": "a6350a577e531a76d89b24942fca3073"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/nav_bar.dart", "hash": "55f6efa34626128dab35b6985aaf09db"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/magnifier.dart", "hash": "f6a7d78c53bba84751bcdff0a232e6a9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/app_bar.dart", "hash": "1ace25dadd2dacbee3c91f367c9429c1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.2.0/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/platform_menu_bar.dart", "hash": "9590cac03fb5c0de43845ee528380b4e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/spell_check_suggestions_toolbar.dart", "hash": "173ed9cde7e32db3e5ff3867c12b7824"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_spinkit-5.1.0/LICENSE", "hash": "4a6a9b806b898dc68a1f890ad1564a10"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/raw_keyboard_ios.dart", "hash": "389764c8c9255efdedf9843e16b3d235"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/toggleable.dart", "hash": "df738d0b6e09c730283cc61d14ce2ded"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/ink_ripple.dart", "hash": "8ca934b02a5298b8f21a63ed650739b4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/animation.dart", "hash": "29a29ed9169067da757990e05a1476ee"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/utils/math_utils.dart", "hash": "e4ee21048ab83cc50d61ac3784afa9f5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.5/assets/CupertinoIcons.ttf", "hash": "42d5bf7c22ac609351e84dbc39b12bf9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/container.dart", "hash": "e41783201fd039e1336413dd423a5796"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/placeholder.dart", "hash": "5bae94050956f893609adf91da0b7e13"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/error_helpers.dart", "hash": "39221ca00f5f1e0af7767613695bb5d2"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/asset_manifest.dart", "hash": "86b4658ee32f155efb8c122ef85911ef"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/LICENSE", "hash": "22aea0b7487320a5aeef22c3f2dfc977"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/text_form_field.dart", "hash": "ed3dac05e5ff3d7e183323d52491c48e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/gestures.dart", "hash": "55324926e0669ca7d823f6e2308d4a90"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/rendering.dart", "hash": "72c648384f9af54c9963265d96c87278"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/physics/gravity_simulation.dart", "hash": "d4634256b002bc534042b99ffbdde402"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/physics/clamped_simulation.dart", "hash": "a753413d3971339169c4a103d7ee3f6a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/spell_check_suggestions_toolbar.dart", "hash": "b90ed671e7e766e8a27de8544ddbdcf4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/quantizer_celebi.dart", "hash": "f12f9a9b8bb504f4617bfd1c00d403f0"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/slider.dart", "hash": "e901ff12abcd3dd4ca3b79cc732d1b1d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_fruit_salad.dart", "hash": "3c8d2d2b73f69d670141d376642e5252"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_monochrome.dart", "hash": "66272a6751b167051ba879724cfe5749"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/semantics/semantics_event.dart", "hash": "e8f704ef18663c53424e306c38d97d39"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.0.17/lib/url_launcher_android.dart", "hash": "680af3f192fdf8a3a894144bdc4cff2d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/geometry.dart", "hash": "5c89cc28cd666aa1708dffaff6cc8c35"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/navigation_rail_theme.dart", "hash": "34ec8e649166b192586b754ce67094da"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/src/point_provider.dart", "hash": "7504c44d1fa6150901dd65ec78877be0"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/choice_chip.dart", "hash": "51f656b4d880a885413a2c963bccfd3a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/constants.dart", "hash": "0cb06ef1fbbec09f85b6b40cdeaa2f9a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/app.dart", "hash": "4a7939e729d46f63553f500041dc7672"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/quantizer_wsmeans.dart", "hash": "6c6dfd5ba4546c1f32201555d6cff215"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/data_table_source.dart", "hash": "0145529858ad246065f7145bac7aef99"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/tab_view.dart", "hash": "81ca0774ea5d4ff5f061ac7b53957e6f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/lsq_solver.dart", "hash": "234f5667a312bcca30a59e788fe46424"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/navigation_bar.dart", "hash": "01f79859a59693addf84974953e7f9c2"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/button_style_button.dart", "hash": "e08b76d444c9753d483bf6975ce61fe3"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/selectable_region.dart", "hash": "9f13e4907afe8f2f9ed0576085d8d0c6"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/input_date_picker_form_field.dart", "hash": "0f46992262b5e9def5963ee29a6b9881"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/raw_keyboard_fuchsia.dart", "hash": "a8abf3052eb97ac137ee94d906fcbd08"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/automatic_keep_alive.dart", "hash": "97fb5affb10598f0356958c2e7e542b2"}, {"path": "/Users/<USER>/Documents/On-going/Flutter/Vguard/vguard-multiplatform-sample-SDK410/ios/Flutter/AppFrameworkInfo.plist", "hash": "5eb1ee18836d512da62e476379865f8d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/spell_check.dart", "hash": "c6cd5ec6babebe0af7224db7ef8fd680"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.0.1/lib/url_launcher_linux.dart", "hash": "cd6ec63f7e53e31b1a8a5ed8a84995bb"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/_network_image_io.dart", "hash": "682907a0e9e60ab53b752dde1e45381a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/material_state.dart", "hash": "3f3f810d0df6a4d8fa696fb14a522199"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/physics.dart", "hash": "6e29d5e69c5745a45214fe14da377c1a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/ink_highlight.dart", "hash": "53af78690831d6aeb88928d8270c21ee"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/tab_scaffold.dart", "hash": "f203c0a13342dd79b78b56ff66fe665b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/icons.dart", "hash": "81c893b7b7339a7d3d6c16614e71163b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/popup_menu.dart", "hash": "605a2aef24ebcbd88042fc543e69a82a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/text_selection.dart", "hash": "6d4775307a2bf338997772562d4467cd"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/activity_indicator.dart", "hash": "55ae9ec969fc6f2f12ba4b96872fc552"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/shader_warm_up.dart", "hash": "660924f2468f228b5a32596ae5bb66c5"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/title.dart", "hash": "dee4f18e2804e238c57a305ccd28eb85"}, {"path": "/Users/<USER>/Documents/On-going/Flutter/Vguard/vguard-multiplatform-sample-SDK410/.dart_tool/flutter_build/efda113092a9e81ac9f0b9b3917e64ba/App.framework/App", "hash": "df605162e595137efa4f4af2ac5b6506"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/list_extensions.dart", "hash": "9f8b50d98e75350b41d40fee06a9d7ed"}, {"path": "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-ebybunlcvccxiqanqwotqbhzsjgz/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/isolate_snapshot_data", "hash": "e2448f756d9085ee22c2992bd3a37a7a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/matrix2.dart", "hash": "7f164e577cfcf8c8295947195cde2a7c"}, {"path": "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-ebybunlcvccxiqanqwotqbhzsjgz/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/vm_snapshot_data", "hash": "37517f2ff904e2bb38bcdb87c3b2935c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/keyboard_listener.dart", "hash": "7e7b2010d6453107351c17753d81b0b2"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/time_picker_theme.dart", "hash": "7a582e4809f3b12a8eeb2aeafd69fa61"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/combined_wrappers/combined_map.dart", "hash": "13c9680b76d03cbd8c23463259d8deb1"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/binding.dart", "hash": "9fa617fc06f3cc28a784af28fdfaf536"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/navigation_rail.dart", "hash": "bad351fb6af0e461426ed48dc007c6f2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.3.0/lib/src/grapheme_clusters/breaks.dart", "hash": "359388897ae53df8791213c31ef05fe6"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/restoration.dart", "hash": "89cb61ecfad952e35daf52caac600894"}, {"path": "/Users/<USER>/Documents/On-going/Flutter/Vguard/vguard-multiplatform-sample-SDK410/lib/main.dart", "hash": "0e0a0baf9bfcdecabaa5fe606b146b65"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/text_editing_delta.dart", "hash": "e74977ba262a820189b7854350bf9af4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/physics/utils.dart", "hash": "727e4f662a828d4611c731f330a3d79a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dynamiccolor/material_dynamic_colors.dart", "hash": "81bf43e01741bf8b9df15ec37ffbc9ea"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/text_editing_intents.dart", "hash": "7776c5eaa171bb5e03d1945d85354f49"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dynamiccolor/src/contrast_curve.dart", "hash": "9a12cf2a3549924510006db4651a1743"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.3.0/LICENSE", "hash": "d2e1c26363672670d1aa5cc58334a83b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/selection_area.dart", "hash": "393af2615234850ced0bb056b30fd133"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/tab_controller.dart", "hash": "e2ecb5f050ac73e6ea80ccc06cb9d7d3"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/sliver_fill.dart", "hash": "0cae216bb3fa19f2f716c7f416500acc"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/range_slider.dart", "hash": "bf286075da7a9e92f62095e254609418"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/input_chip.dart", "hash": "7397bb1624b672abd4672aaec8334149"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/vector.dart", "hash": "6a67d38bafe568f1b4047286d586fbbc"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/action_buttons.dart", "hash": "7aa7a3f6fe7120292c1dd2eeb95eda39"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/sliver_prototype_extent_list.dart", "hash": "8c3714021359cb875206d486f25ee2c9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/snack_bar_theme.dart", "hash": "9638263938be80353a032c8e789eb692"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/linear_border.dart", "hash": "1916c4e665bb480220d0e6dce3b9400f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/interface_level.dart", "hash": "dfe858a6ed0de97f6c6de45176f474d4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/sliver_resizing_header.dart", "hash": "a619d972c76b44bc8d1c3dc88c8445ec"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/scheduler/binding.dart", "hash": "594fc6041eebf4706476277d9d12e659"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/_platform_selectable_region_context_menu_io.dart", "hash": "61af6ead2e2dc04677bcfb8c0c2104ab"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/menu_button_theme.dart", "hash": "21cd40fc2ea0defcdc048d54b77722c9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.0/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/form_section.dart", "hash": "30a8a2a67dcb5b0a7969ce9bf1509129"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/hit_test.dart", "hash": "c8bb12d41ce48bb27184ddd257d2859b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/flavor.dart", "hash": "4744aaec510cd9c8b07ca129362b8fb9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/semantics.dart", "hash": "4b784d6e4f290bd6d5a1f38bfb5701d8"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/synchronous_future.dart", "hash": "b2516cc7704e0c10a5f1d777ac857ea6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.3.1/LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/radio.dart", "hash": "228535e61392d353222c93613f49eb9b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/sliver_tree.dart", "hash": "5ebb4923442d1fcc7c91f9677e086467"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/binding.dart", "hash": "d9b79784fbfdc586f6b715fb11537767"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/equality_set.dart", "hash": "4b5d82ddeb09bc46ae0e980616ce0109"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.1.0/lib/src/types.dart", "hash": "c9f68b9d0206a1a71be33f572d419b74"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/data/pause_play.g.dart", "hash": "8ebc4ef8486c9875330658ed1a145020"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scrollable_helpers.dart", "hash": "b1ebe2e5cdcee36b19b606b37a12cc38"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/raw_keyboard_android.dart", "hash": "d0495513468d137435fad7178ad39b4f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/platform_selectable_region_context_menu.dart", "hash": "db1783b3083765425632b2ca451dbbc8"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/list_tile.dart", "hash": "c8266c3435b50929eb834df245aa2544"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/multitap.dart", "hash": "87fd57b3136ca33a1e97c5524b74e112"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/sliver_layout_builder.dart", "hash": "e7d84c68f69f7f105e4acca6946ded83"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/widget_state.dart", "hash": "91f052d253200c68fd039b858db9d7ec"}, {"path": "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-ebybunlcvccxiqanqwotqbhzsjgz/Build/Products/Debug-iphonesimulator/App.framework/Info.plist", "hash": "5eb1ee18836d512da62e476379865f8d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/status_transitions.dart", "hash": "d37e33aaef71722417cb64537e97092d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/box_fit.dart", "hash": "********************************"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/page_storage.dart", "hash": "20ff58eb86f7132e7b2a18f0442305e6"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/adaptive_text_selection_toolbar.dart", "hash": "8b6832f29637935d19be203efb2b1283"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vm_service-14.2.5/LICENSE", "hash": "5bd4f0c87c75d94b51576389aeaef297"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/physics/simulation.dart", "hash": "c0fe6462e3a08d6d6afbf4f66130d494"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/banner_theme.dart", "hash": "15951ad2d184fb64e0327b35f1ce65df"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scroll_simulation.dart", "hash": "366aa23421c294b9ad3fa22271afbdb3"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/oval_border.dart", "hash": "9ca011fc6acdcd04949fc9c6ec849043"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/checkbox.dart", "hash": "********************************"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/animation/animation.dart", "hash": "efb0eae05f940ace69b5be93759acd1a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/image_cache.dart", "hash": "11c860591b76d1994f12042408831c94"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/progress_indicator.dart", "hash": "449d44061d435c8bbc5366d6eacf065a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/blend/blend.dart", "hash": "f487ad099842793e5deeebcc3a8048cb"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/image_stream.dart", "hash": "facf2204744818c1a3a587be1d7b7645"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/canonicalized_map.dart", "hash": "889042dc1cc5b1f4e4e1572270920f54"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/gesture_detector.dart", "hash": "a7d0241b77157594463b3f72e282b2f3"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/paint_utilities.dart", "hash": "0491e1cca60da329c2e03c48abde07c9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/toggle_buttons_theme.dart", "hash": "eca62c60db96d71f3cae9b506875c03a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/hct/cam16.dart", "hash": "ca959e5242b0f3616ee4b630b9866a51"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/LICENSE", "hash": "1d84cf16c48e571923f837136633a265"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/team.dart", "hash": "f6c6b31745eec54a45d25ffe6e5d7816"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.0.1/lib/url_launcher_macos.dart", "hash": "f3ab5dd6ac3e002f5c3d63737cbb82cc"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/floating_action_button_theme.dart", "hash": "faf51c4fe1dc7af7fabc7c78a960305c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/responsive_framework-0.2.0/lib/responsive_row_column.dart", "hash": "89850b6ca8db1f7aa91ddad76a8c57db"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/deferred_component.dart", "hash": "f7b634b150a8381c9b4c03482a0d6e6d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/elevation_overlay.dart", "hash": "5eaed6b1fcf32a11b53e5dcf27ae101c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/algorithms.dart", "hash": "5fac07b9706002db32a4c5f6698cea58"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/text_span.dart", "hash": "99f994fae7b85fd2e6cfe48e211da58e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/system_context_menu.dart", "hash": "2854423575c903818dc71d712b749e71"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/scrollbar.dart", "hash": "a6c5b8639baab209a7d53dc7e0d1cd1d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/scheduler.dart", "hash": "95d8d1f6a859205f5203384e2d38173a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/button.dart", "hash": "212fbd7b9d63586beed8d0e16701bf05"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/tweens.dart", "hash": "959489b18fda284c434701586b43c66b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/outlined_button.dart", "hash": "8ac1b57ab29335fd50ea76229944a074"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/visibility.dart", "hash": "043377dddf07af1face4788c64ab583f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/animation/curves.dart", "hash": "5d9eccb0fcbc01b2c727d80f27c5631b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/equality.dart", "hash": "4cbe8ed92ec76b5cd80e685ba71acdb4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/placeholder_span.dart", "hash": "e8106b34b25812580ba75dea86a5a096"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/flutter_logo.dart", "hash": "32187ab06a29c3f5929b9f26fd5ccb8b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/dropdown_menu.dart", "hash": "557fc80bee02db6922b6c67f0a40cd34"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/autocomplete.dart", "hash": "67795683a1a7b3dac0854a1e5da146ac"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/colors.dart", "hash": "f3afc3b9324005ca112ccef93598cd89"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/matrix_utils.dart", "hash": "51bd3df911d2f628879de56dcf93d74e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/data/menu_home.g.dart", "hash": "24cdd2cb365ef36394210a26c9fb1dda"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_rainbow.dart", "hash": "0bc80db5885f9d8ecc0f80ddab6fe8b4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter_tools/lib/src/build_system/targets/native_assets.dart", "hash": "a4e3123f76e135cc40ea3aa0efd2e558"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/platform_views.dart", "hash": "b40780510c9b3d671dd86b07b1f812e9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/routes.dart", "hash": "b31c6998027d8e37855bdc3be9702803"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/time_picker.dart", "hash": "70897dd932d388476b6a9a96fe349e25"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/arena.dart", "hash": "b3dd803ac13defc89af5ad811ea2ca31"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_web-2.0.12/LICENSE", "hash": "c458aafc65e8993663c76f96f54c51bc"}, {"path": "/Users/<USER>/development/flutter/bin/cache/artifacts/material_fonts/MaterialIcons-Regular.otf", "hash": "e7069dfd19b331be16bed984668fe080"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/data/add_event.g.dart", "hash": "dd25c518d50a5334f0a231570f7c919b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/magnifier.dart", "hash": "cfad5d08fc946a2e0a67e46bf582130c"}, {"path": "/Users/<USER>/Documents/On-going/Flutter/Vguard/vguard-multiplatform-sample-SDK410/.dart_tool/package_config_subset", "hash": "83674ffb54fb25b116b711bd42d59c52"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/data/view_list.g.dart", "hash": "ad6bf1d7b3079f5be69fb40ada4fc145"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/gradient.dart", "hash": "831f900bdcad04be13af3338451e99ee"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/floating_action_button_location.dart", "hash": "76f393e988aadd48d101cbdb0255434f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/responsive_framework-0.2.0/LICENSE", "hash": "ef7caf4727a0bc7a6411382e62e76a6b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/table.dart", "hash": "46d984bdb7a861219c238974d6bbade4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/service_extensions.dart", "hash": "0eef32ab9b2cf423c48e89f2dcd9bd6b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/cupertino.dart", "hash": "e093bedc58f3a92cb4ab66556b4ea9c8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.0/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/annotated_region.dart", "hash": "3bc33c65fa44a57d13430fdedef82bc2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uni_links_web-0.1.0/LICENSE", "hash": "264b36239eec305d387c052ae4a2a35c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/recognizer.dart", "hash": "4af4d6cbc2123755fc589f044bac8d6c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/tabs.dart", "hash": "ccf1ac4d4f2404b45b65e33a068b3e8d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/data/play_pause.g.dart", "hash": "cee61ff4bc1494858ec39f8c4f09c1a6"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/continuous_rectangle_border.dart", "hash": "195ce88bb0f906dd500f3ee23c097a95"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/mergeable_material.dart", "hash": "0373ba3e37bb0de4333d914284042952"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/drag.dart", "hash": "d5cd032ed35d1180819dac9428cd1035"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/two_dimensional_scroll_view.dart", "hash": "8518b1367156079d21cbecf7217d2152"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/circle_avatar.dart", "hash": "58910ceafe966e76293cc267537cdc13"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/hct/src/hct_solver.dart", "hash": "b972c32590c642256132827def0b9923"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/tween_animation_builder.dart", "hash": "5b284216cdaff11bd25b0175739bf48b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/text_input.dart", "hash": "576b6e090503e6ebeba30801482d3b66"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/viewport.dart", "hash": "e797d0f85b6b031854f48a68e6d9f9de"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/basic.dart", "hash": "f3a8fcfcd79b619cc886547b8ff9b25c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/memory_allocations.dart", "hash": "75c38766ddb6a4505dc9271c6a9fec49"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/page.dart", "hash": "4ed1ea1e77b6220b80b81d0f65976c73"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/carousel.dart", "hash": "22dc080522882b010d90c7010b5aeba9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/events.dart", "hash": "c0fcec4e410b3a54af8d2c09e4f6ef77"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dynamiccolor/variant.dart", "hash": "8dea906a9b8773920b6d1ccea59807bf"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/icon_theme_data.dart", "hash": "eca4f0ff81b2d3a801b6c61d80bc211c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/navigation_bar_theme.dart", "hash": "b12f18fd97ffec06b763749adcd080be"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/data/menu_arrow.g.dart", "hash": "280f78984a3d21c2b797d427c12b4c4e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/plane.dart", "hash": "f0c6d5d05fbdc95ab84f1a63894b7be6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dynamiccolor/dynamic_color.dart", "hash": "7ffb6e525c28a185f737e3e6f198f694"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.3.0/lib/src/grapheme_clusters/constants.dart", "hash": "9f9b79f577d9fdf4f20c17a26a2f1d57"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/object.dart", "hash": "daa0c9b859ed1959e6085188a703f387"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/inline_span.dart", "hash": "8831386d40ad4cf7660a3266307625e1"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/page_scaffold.dart", "hash": "27006efbb2180e9e1afb93a52e89dbe8"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/licenses.dart", "hash": "365a4e930595816e20608286252762dd"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/viewport.dart", "hash": "9c0e3742a2b56252c568e7f0a0af8810"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/disposable_build_context.dart", "hash": "cf064f126091733539cece8deeefa1d0"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scroll_context.dart", "hash": "22c35af71293a579bba619b03228367c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/action_icons_theme.dart", "hash": "3fd3c4bcbbf54fbcad3b048e3c89d43f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/colors.dart", "hash": "5ed8acdae7dd3501b64b0ff3e33c1f45"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/sliver_group.dart", "hash": "5b3185ef333a9582c4a7bba6185e7ed7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/contrast/contrast.dart", "hash": "0c9bd1af5747fd55e7488c731ad32dee"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/triangle.dart", "hash": "7d2bdb4801fc8b3a110f36d5e5fa59f5"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/system_navigator.dart", "hash": "3b98354978b0f9b4903f388399b3b8e0"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/context_menu_controller.dart", "hash": "2c6d411ab41668e38c9c7e50dff8980b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/curves.dart", "hash": "02be76cbb0e6152f7162d22383971f51"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/src/point_provider_lab.dart", "hash": "6566a35ff0dea9376debf257bdb08fba"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/timeline.dart", "hash": "8d529903ab8e80df00c0859464bfbc46"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/debug.dart", "hash": "3ff5c718c1926e8652d8ed393c236c7a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/text_selection_toolbar_text_button.dart", "hash": "a0a1a162853c04dfcdb92b1a910488b7"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/box_shadow.dart", "hash": "********************************"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/error.dart", "hash": "b831e4cd07f0e2ad701fdf6ac1dafe19"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/restoration.dart", "hash": "ee984ad6a59ef4e7fcf5caa40736878c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dynamiccolor/src/tone_delta_pair.dart", "hash": "f5b38c21bf580c89610a8b58c65aae00"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/color_filter.dart", "hash": "89862172ecfdefb923b68111e9a86fa1"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/image_decoder.dart", "hash": "32d8ff829d8956046c0a91c8ae4160a2"}, {"path": "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-ebybunlcvccxiqanqwotqbhzsjgz/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/NOTICES.Z", "hash": "d86ebcd5133b39c0a8ad727ff0d6f68e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/data/menu_close.g.dart", "hash": "fb3f068735531a31f3d1253216051136"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/layout_builder.dart", "hash": "eded57cbe50a1ee3c706e7f683e68222"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/queue_list.dart", "hash": "02139a0e85c6b42bceaf3377d2aee3de"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/selection.dart", "hash": "35b81a5b6064d11e63b86458f991fbf4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/animation/tween.dart", "hash": "1a0a7a32ca4a38d882836e00d31cd8b7"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/slider.dart", "hash": "c5a71d0fdc87177c4ceabc19fbf2d251"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/empty_unmodifiable_set.dart", "hash": "d2e49f7a3cc02c7bd120dd5e4b9daa33"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/text_editing.dart", "hash": "a1497040765f0f7199f990aa00af5310"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons.dart", "hash": "26e8edddc50361d04ffdac680bcfeeca"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/bottom_tab_bar.dart", "hash": "360dd05d7395f54f7f785d6f8c36a191"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/action_chip.dart", "hash": "aee97b3fb21f5d87cc7a4c1079c77aa4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/physics/tolerance.dart", "hash": "f75f31535e16b018e2a5f9a968b7254c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/elevated_button_theme.dart", "hash": "86b06851f3ff5ee17bb39fd0d241cbb9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/checkbox_list_tile.dart", "hash": "********************************"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/autocomplete.dart", "hash": "872203d79024fa48a492a03eb0708499"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/_bitfield_io.dart", "hash": "64b62181e86fc993a65f92bdd4a9bc5d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/aabb3.dart", "hash": "257ca4608e7d75f1db8d4c3ab710ac70"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/badge_theme.dart", "hash": "f179cf16ea560111839fc980420e3b18"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/text_layout_metrics.dart", "hash": "13be7153ef162d162d922f19eb99f341"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/sliver_padding.dart", "hash": "9568f208277ebd33bf390ffdee55c753"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_fidelity.dart", "hash": "553c5e7dc9700c1fa053cd78c1dcd60a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/form_row.dart", "hash": "fdbf119306639c731a4d90a5f56cee7c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/focus_scope.dart", "hash": "c9391709a58b30c3f3812cc2b5e249d4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.0.1/lib/url_launcher_windows.dart", "hash": "c94a1294719330fc92a612cd2d3d89ac"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/app.dart", "hash": "40a19c80c10d11c793a04d0d81a8c16e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/layout_helper.dart", "hash": "3f1936af23dbdc313352c2213f4c2dfb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/responsive_framework-0.2.0/lib/responsive_value.dart", "hash": "df10a4a446accc1d1ac1c21e0f765964"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/sliver_fixed_extent_list.dart", "hash": "66650a747992c52961dad0ac63628efe"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/constants.dart", "hash": "c7cc72c1e40d30770550bfc16b13ef40"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/gesture_settings.dart", "hash": "a26d02dca4465f31f1b4ff143b615052"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/debug.dart", "hash": "34df6a85c7662f602e66bba150c4fab4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/elevated_button.dart", "hash": "05ae77852a25316df035464cb7ff2509"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/paragraph.dart", "hash": "78fbd8387fe15451421bbe059e3deeb8"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/process_text.dart", "hash": "8c3499889c31838ff4de84d56ebbdebc"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/app_bar_theme.dart", "hash": "2f92c28411483032fb7c0a851ebbbb5a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/text_selection.dart", "hash": "de3213b3d5bc998d1d921b4ce782f91f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/monodrag.dart", "hash": "fa4de83c5c418c49affda2b164993f99"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/observer_list.dart", "hash": "074b866f17aee09c76583b075e83cb8c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/primary_scroll_controller.dart", "hash": "1c43aa902b27d1a8936c77dcf231953b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.1.0/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/dropdown_menu_theme.dart", "hash": "aeaa12c1af305eb8e588f3b7bec09ab1"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/live_text.dart", "hash": "7da554c3a69a1c2d019202e3f63331c5"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/context_menu.dart", "hash": "679653d6c84dbbb523d31feca1431de5"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/picker.dart", "hash": "0a816e62151c063e79b644989abe3075"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/bottom_navigation_bar_theme.dart", "hash": "c970404e32ab9a5917d955f66c830b1e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/inherited_model.dart", "hash": "8635fbec89c2cc03404a2a3233d31bbc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dislike/dislike_analyzer.dart", "hash": "d7eb1678ec74acd9857a4193fd62ed5b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.15.0/lib/meta_meta.dart", "hash": "8b83501f9451392bceda63c9281db57d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/force_press.dart", "hash": "bd21408997d44d0bd83cf6d38bf3d2a2"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/value_listenable_builder.dart", "hash": "ce2f733c9ef461c84bb85a93bae9e7a9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.3.0/lib/src/extensions.dart", "hash": "38e17b28106d00f831c56d4e78ca7421"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.0.17/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/boollist.dart", "hash": "206ef1a664f500f173416d5634d95c8b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/semantics/semantics.dart", "hash": "0381c11b6d4f8c985d498d475bc82822"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/decoration.dart", "hash": "975c2fb9e84493af10e44456cad39bc4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/button_theme.dart", "hash": "02eac755aab5bcb73ac9a278a117ca1c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/utils.dart", "hash": "fe2489ea57393e2508d17e99b05f9c99"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/notification_listener.dart", "hash": "11a634821b3bce05dac94f3dabe52a75"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scroll_metrics.dart", "hash": "a7a9eeb4bfc63b4f552162a16b62f70a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/inherited_notifier.dart", "hash": "f4f97e64864383af2f259063e32bcf49"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/_isolates_io.dart", "hash": "b5439c33692d13cbf7df2d19d9713345"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/restoration_properties.dart", "hash": "e438b8b77c0b056309e25325952b64f6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.3.0/lib/src/characters_impl.dart", "hash": "3bb0652e163327c58784ce2a2b882a7c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/scaffold.dart", "hash": "4e8c9de420d0f15c8d396497675bfeb3"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/icons.dart", "hash": "8aa656cf88546009f56e54df825c9915"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/filter_chip.dart", "hash": "2d7c9fe1f427f655c48bac29cbf4ac3c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/search_field.dart", "hash": "7fc0ae9c724cfdffdda5de63fa6d0972"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/route.dart", "hash": "bd7e1a7092d5b455dcd4ba18801ffb5f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/union_set.dart", "hash": "0073f703be7f7ddbd7f04d1b740f35c6"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/transitions.dart", "hash": "327867a2784fcc92c5a1737cee7a3197"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/quantizer.dart", "hash": "db799bf48af97b7c0edc93ad96b4a6da"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/flex.dart", "hash": "1e20ba56045ec479eee76a7a78127684"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/box_decoration.dart", "hash": "********************************"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/time.dart", "hash": "71462b0c828c47fad9b2ef1ef68cd9d1"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/icon.dart", "hash": "20fbf0ae1f42909e7806add12b2c6e3d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/score/score.dart", "hash": "58b9bc8a40fd3e2f7d9d380d0c2d420f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/search_anchor.dart", "hash": "28dc34f687478a2897fafbaafa053b92"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/debug.dart", "hash": "51fa10cf30bde630913ff4c6e40723ba"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/desktop_text_selection_toolbar_layout_delegate.dart", "hash": "bd34896b1432d6f707498d3df7a7c3ae"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_neutral.dart", "hash": "3ee18da390e16ca65f2ef168adb8a1ef"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/matrix4.dart", "hash": "b5f0b0da99e8a07d58c21ae071800404"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/adaptive_text_selection_toolbar.dart", "hash": "c23351f27a693e0330fc77704443a83b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/vector_math_64.dart", "hash": "bd1315cfa157d271f8a38242c2abd0d9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lints-2.0.0/LICENSE", "hash": "4cb782b79f6fc5792728e331e81a3558"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/consolidate_response.dart", "hash": "b869c4e930ab3313f9b1d196d532d3dc"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/keyboard_inserted_content.dart", "hash": "f27209609f9689165f058b3ca18165d9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/noise.dart", "hash": "206b1db3ce5f7b9e5efd220712f8d391"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/button_bar.dart", "hash": "99f17d3841e146c27a0079f86b453123"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/dual_transition_builder.dart", "hash": "2570eaf33e6ce252fa201989b9ee6af8"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/page_transitions_theme.dart", "hash": "c43806aa723e38c3c106b7655b02eabc"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/data/arrow_menu.g.dart", "hash": "3ec0013bd7ba2e0f89cb963f867f0d96"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/border_radius.dart", "hash": "86e192e4762ca754c4af48f3fea22501"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/pointer_router.dart", "hash": "6e800790e7858e8e1cdc73c8cc09d719"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.2/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/sliding_segmented_control.dart", "hash": "25f82e13ae2a60861c029aed5f4d8c92"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/async.dart", "hash": "642ff6f56b746c990445d42331e8ff81"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/combined_wrappers/combined_iterable.dart", "hash": "67d16e841606c4e5355211fe15a2dbfd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/combined_wrappers/combined_iterator.dart", "hash": "6c54f90e0db5f42a13be6b3efeb4a04d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme.dart", "hash": "a6adbe3868e017441360895c35fd6aa2"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/context_menu_action.dart", "hash": "537e9b325c6baa5e02649883f515cb86"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/focus_manager.dart", "hash": "819e1a61d059020752de65cd6e5b8466"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/responsive_framework-0.2.0/lib/utils/scroll_behavior.dart", "hash": "f223167f22ed0a03f66cdfda5fbb9c8a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/progress_indicator_theme.dart", "hash": "8effe6176ace6ada9ad1db0370cf2e78"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/matcher-0.12.16+1/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/filled_button.dart", "hash": "b2cf47ccd5a6cf4843108c3a9f821c55"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/grid_paper.dart", "hash": "f76941994ddf30e398313421f1588d85"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/converter.dart", "hash": "22fdfe2139eaf6759fc40d9fa5fafab4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/icon_theme_data.dart", "hash": "ab91622a9d9c558bb65f0f06b904d873"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/toggle_buttons.dart", "hash": "6b06971a44e8eed786f2f06388d0580d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.0.1/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/basic_types.dart", "hash": "3126d82e17646add4e00c2099ec262ee"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/search.dart", "hash": "51d9fc6dac581a413a064529ebca2aeb"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/text_selection_toolbar.dart", "hash": "38e1d0509dc1ed42b630c2604c905593"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/texture.dart", "hash": "888c72929d9b3cd94975f06965e72976"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/data/event_add.g.dart", "hash": "70b3c5178a2900b73be78d52770fcd40"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/text_field.dart", "hash": "9003679f83073733248c1bd73d3097e3"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/actions.dart", "hash": "eb295ba3319c0f2b1c910d981594bab7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/LICENSE", "hash": "3b954371d922e30c595d3f72f54bb6e4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/_html_element_view_io.dart", "hash": "54d59a18ed489222e79e19304ca89cc9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/image_icon.dart", "hash": "479493da08b4e2137fc162ff23bef99b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/tooltip_theme.dart", "hash": "6f281d35c9453eb6092c1addcb79055e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/constants.dart", "hash": "be94b8f65e9d89867287dabe5ea1dff1"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/text_selection.dart", "hash": "b32a871f85e9be997f09ce9bc7a7df32"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/mouse_cursor.dart", "hash": "d64508f06238e79c4c99b1af662963f5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/responsive_framework-0.2.0/lib/utils/responsive_utils.dart", "hash": "7a60ff5eaf05c16f3214b46f70bea11c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/editable.dart", "hash": "c0d8bc79666e26dcbc4f56428478d7b7"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/scrollbar.dart", "hash": "03663b6e95c127f3c83bb84ed2994bb2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/utils/string_utils.dart", "hash": "603b7b0647b2f77517d6e5cf1d073e5a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/about.dart", "hash": "3b10ed50f64576ade40f060080b86ff5"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scrollbar.dart", "hash": "7826320e6f3daff8567f45add54c141d"}, {"path": "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-ebybunlcvccxiqanqwotqbhzsjgz/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/AssetManifest.bin", "hash": "693635b5258fe5f1cda720cf224f158c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/input_decorator.dart", "hash": "289dd9662b900b118f3042be16a73987"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/bottom_sheet_theme.dart", "hash": "87e638fbc5e15e8d93ef84462a09bcf5"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/feedback.dart", "hash": "268c67f634fffcd852c6fc020d6ed0fe"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scroll_physics.dart", "hash": "909cb251e671fa02581480629642a663"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/star_border.dart", "hash": "d51d434283a0193da462cab610325483"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/print.dart", "hash": "824dffb7b5c1cc401a975820f0085fa7"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/text_style.dart", "hash": "d2c684f89d90661960c497d7f4faa906"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/default_selection_style.dart", "hash": "2f21ecaf225265e45135854c47dfed90"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/service_extensions.dart", "hash": "b5d6c349fa0259f1516951989e4d5bbc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.15.0/LICENSE", "hash": "83228a1ae32476770262d4ff2ac6f984"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.3.0/lib/src/grapheme_clusters/table.dart", "hash": "29e1858c5ebc2b4dc6d1528196bfb1b6"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/overscroll_indicator.dart", "hash": "5a625dd07fb7a6dc55fa0f5c063a2f91"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/animation/animations.dart", "hash": "c95cc3d4d3122294945f603ec0b3132a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/decorated_sliver.dart", "hash": "190d05ecf34fbb2fd698148bc68cd5b4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/semantics_debugger.dart", "hash": "e0e33434911ce4e90380de00d4f00671"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scroll_activity.dart", "hash": "f5b4267f1c1f72ab634a2be53517d1a1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/obb3.dart", "hash": "5ca0b5786bf63efd4fc72fcecfe1b36c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/palettes/core_palette.dart", "hash": "d35b72b249d19f54a4cd6f22ff3299e9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter_tools/lib/src/build_system/targets/ios.dart", "hash": "cefbbc6561815c921c80942954459688"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/slider_theme.dart", "hash": "314b4e3c61b5f1998e51519f9d412beb"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/hardware_keyboard.dart", "hash": "5d93a9e5daf8f93e7820b5a2d1fa89d3"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/popup_menu_theme.dart", "hash": "ac5fe86ab9ecbd33f878f0a580f3bfa7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/iterable_zip.dart", "hash": "df699735e3bcd730f16ce377d562f787"}, {"path": "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-ebybunlcvccxiqanqwotqbhzsjgz/Build/Products/Debug-iphonesimulator/App.framework/App", "hash": "9719dc6515b212a3870e67aa76d5444c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/ticker_provider.dart", "hash": "7e3157d6dcf4be11dd91edc6077e6401"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/ink_decoration.dart", "hash": "bed0fb96275252e2297091fd19c45ee7"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/clip.dart", "hash": "fdafd11afaf787fce66b7f5890d21241"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/list_tile_theme.dart", "hash": "15378441d5acd43ee5e67372183251e2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-0.13.4/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/menu_theme.dart", "hash": "887a4888dd10dc19020553757a12bf31"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.0.17/lib/url_launcher_ios.dart", "hash": "9198a73078a7db98592d230561e3b5f6"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/flow.dart", "hash": "79ac8ad87caa659775def3b2860e5a87"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/image.dart", "hash": "8d7a3417ced5623477f0ae66b4693574"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/unique_widget.dart", "hash": "11b4d96c7383b017773d65cb2843d887"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.1.0/lib/src/url_launcher_platform.dart", "hash": "2cf0dde581bc56fbf8aa545238eb07f5"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/fractional_offset.dart", "hash": "0a2cf42cdd64530e5ca9a120eda90f12"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/material.dart", "hash": "ac787747162d980c88c97f09723f31b9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/animation/tween_sequence.dart", "hash": "ceca8c46e07b211bd755e480b1bd6b32"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/diagnostics.dart", "hash": "fb7de9357b27176361434afe3ef378c4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/text_selection_toolbar_button.dart", "hash": "7e4502c6962965fa58f185d707a72afc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_easyloading-3.0.5/LICENSE", "hash": "102fbf4193cffa8a7b38b0a1b27abf0e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter_tools/lib/src/build_system/targets/icon_tree_shaker.dart", "hash": "cbe9b4803b3db39b5367784e37dbe387"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.1/LICENSE", "hash": "175792518e4ac015ab6696d16c4f607e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/icon_theme.dart", "hash": "8df5a0fc260d13ce415e2262527a1f8c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_lints-2.0.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/segmented_button.dart", "hash": "1f9d085b26323e03c82077418ccc0a4d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/service_extensions.dart", "hash": "29114a10bc26482a660a1114749838f5"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/modal_barrier.dart", "hash": "4c0d1712c28161aae922d6fb6aa513f3"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/filled_button_theme.dart", "hash": "9fcf9265f470f44989cf4da88dd7cc0c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/colors.dart", "hash": "34b8a771ced62ddab9512678e7fe3fd2"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/dialog_theme.dart", "hash": "03b0f3319b7390e1f3928ad3e3e544a8"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/scheduler/priority.dart", "hash": "ac172606bd706d958c4fe83218c60125"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/asset_bundle.dart", "hash": "67be4bdf31d93f8a5e654ec21d96ed5b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/adapter.dart", "hash": "0192533b9be6f394b49a75b38f8dc84d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/typography.dart", "hash": "3589ad5e816918a56c21cafcc6e5a611"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/semantics/semantics_service.dart", "hash": "b00868737b95fe1ac169010b28a8d12b"}, {"path": "/Users/<USER>/Documents/On-going/Flutter/Vguard/vguard-multiplatform-sample-SDK410/.dart_tool/flutter_build/efda113092a9e81ac9f0b9b3917e64ba/native_assets.dill", "hash": "d41d8cd98f00b204e9800998ecf8427e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/tap_region.dart", "hash": "248365a073cb187f63e1c3dc8ece112d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/text_boundary.dart", "hash": "51f2a9874a4ed255b69aeb0c3feb1903"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uni_links_platform_interface-1.0.0/LICENSE", "hash": "264b36239eec305d387c052ae4a2a35c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/magnifier.dart", "hash": "d23bcea39c8a0ddcf5600a01de0d4bf9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/editable_text.dart", "hash": "0759c0203bc8cd82a7b99d5683673a32"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/key.dart", "hash": "c214cda31dee52ae8cbe8853acc2b7ac"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/calendar_date_picker.dart", "hash": "53870a85433563b929e07964cff0d2c2"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/binding.dart", "hash": "ddb79a905f493ffa11db04d575d1529f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/autofill.dart", "hash": "ab99fd7503e96bfe98caf7abf357e186"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/keyboard_maps.g.dart", "hash": "6b92d8f12a7fb46649297e25d2cf2b34"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/dart_plugin_registrant.dart", "hash": "44b8efa69ec831d1a0ce74c20ecc27b4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/raw_keyboard_linux.dart", "hash": "7f7ca3170e520952778ebe749d3cef68"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/inherited_theme.dart", "hash": "97af54574da94dbb0a8b5a5549e954b3"}, {"path": "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-ebybunlcvccxiqanqwotqbhzsjgz/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/FontManifest.json", "hash": "dc3d03800ccca4601324923c0b1d6d57"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/message_codecs.dart", "hash": "e62c6008d26fdd56ee11d82ca4f1d516"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scroll_aware_image_provider.dart", "hash": "5d34c419faa453f50535c81a93de00d0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_testing-3.0.1/LICENSE", "hash": "f721b495d225cd93026aaeb2f6e41bcc"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/stadium_border.dart", "hash": "8436323dbb52826a1c0e7b504bc6eb5e"}, {"path": "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-ebybunlcvccxiqanqwotqbhzsjgz/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/kernel_blob.bin", "hash": "fa7d8a004df63ba3e9dd9824205b7d11"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/app_lifecycle_listener.dart", "hash": "1f442d376af9a31939dd759498712154"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/predictive_back_event.dart", "hash": "42808d0d2fbb61569f4cb043ee4ed594"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uni_links-0.5.1/LICENSE", "hash": "264b36239eec305d387c052ae4a2a35c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/haptic_feedback.dart", "hash": "2a90f95a9de0d2364fee5e1ddbab0c18"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/palettes/tonal_palette.dart", "hash": "44b3c2a3d6e67a3213a49cce58fed932"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/data_table_theme.dart", "hash": "5b273bbab831a700c166cfa57a2e089f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/alignment.dart", "hash": "ff44ff979a414bb8029a4e8b205d2479"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/shared_app_data.dart", "hash": "496982c4b90528a5360d8064ddd1373d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/app.dart", "hash": "d9e98b5a81cf86f8c1f595ff861eebe3"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/debug.dart", "hash": "e3e4ea8878b2a7a9cc04137081ae1617"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/foundation.dart", "hash": "55fdc7e9835582d898780b018eaf85d5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/material_color_utilities.dart", "hash": "11df661a909009a918e6eec82d13e3ff"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/tap.dart", "hash": "a050a2931c4a02c78e8216226cee6eba"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/service_extensions.dart", "hash": "540497224c553a9b08b20397bd78ef69"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_vibrant.dart", "hash": "5b04f80518a8417cb87a0aec07dacf4f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/material.dart", "hash": "f3e5196336dc300b73d52ee30ae05c33"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/unicode.dart", "hash": "8b525140e1bf7268e1681a62c7640eea"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/thumb_painter.dart", "hash": "33ef56c795b9e96d640165cecb2f5032"}, {"path": "/Users/<USER>/Documents/On-going/Flutter/Vguard/vguard-multiplatform-sample-SDK410/.dart_tool/flutter_build/efda113092a9e81ac9f0b9b3917e64ba/app.dill", "hash": "fa7d8a004df63ba3e9dd9824205b7d11"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/back_button.dart", "hash": "035b8d3642fa73c21eafbee7851cc85d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/strut_style.dart", "hash": "17c301dab5e5355a9c2683e7f3be7ede"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/binding.dart", "hash": "be313780ab3939da8cf5e43dd81e8ba8"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/shadows.dart", "hash": "c4119b97c9830ac751fca7f2c7989f6b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/table_border.dart", "hash": "718652a205e0c90b9df973c701c36a02"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/viewport_offset.dart", "hash": "60d167d34050e1468a18e6a768d9d2bc"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/date_picker.dart", "hash": "fa3cf16f88096f2239ac79afa6bf6c1d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/custom_paint.dart", "hash": "1b0a0fa63fc381a5f245af8f5d424f2b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/selection_container.dart", "hash": "0b0f625bca76693cdeaa1f4358809351"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/drag_details.dart", "hash": "bc414bb2ae027237bbff00c7b2f2e2a5"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/nested_scroll_view.dart", "hash": "610943df3ed3669788eee55726cb7035"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/outlined_button_theme.dart", "hash": "2b9a24c4f3c66c9847e794ddbd1e7249"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/framework.dart", "hash": "8f3d0a0b22c3d0ada7405e15eb0e7fb5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.1.0/lib/url_launcher_platform_interface.dart", "hash": "9190f2442b5cf3eee32ab93156e97fb1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fake_async-1.3.1/LICENSE", "hash": "175792518e4ac015ab6696d16c4f607e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/priority_queue.dart", "hash": "34a4d340931147322eaddc77fdc65c22"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/refresh_indicator.dart", "hash": "164687f5f7beb0842486a60729330e3d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/checkbox.dart", "hash": "********************************"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/tab_bar_theme.dart", "hash": "40c9adb59d6f9b10d60bbebbd42d57d8"}, {"path": "/Users/<USER>/development/flutter/bin/internal/engine.version", "hash": "8b459c06aa0327d6717eb1bf229ed999"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/widget_inspector.dart", "hash": "d6b57fe12b3df49b484687371c08487d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/segmented_control.dart", "hash": "e31765e74f6120b5bfdf059885643943"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/menu_style.dart", "hash": "04d4cbe0a7a40a31323cd39f2bb8d585"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/vector2.dart", "hash": "6a0fa6360b3aca8deb85dc7d88176eb8"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/heroes.dart", "hash": "ef07fd7a67d6cd5ac37c5c20863af323"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.1.4/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/floating_action_button.dart", "hash": "006f80560d9594a875c5218518a765a9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/view.dart", "hash": "9113446eba03f06e109fba1b295bd26c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/switch_theme.dart", "hash": "444589d7a3a418a8388003283b096007"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/date_picker_theme.dart", "hash": "a1ee439640dc3ff94786e7d96122b671"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/message_codec.dart", "hash": "3b5bc5c0df6b6e1abb26877f612b2242"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/resampler.dart", "hash": "780826ab1f1e8af513298cd5b5bca297"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/image_resolution.dart", "hash": "d012d47412ff8c4fe1cbe6ac1d53b333"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/focus_traversal.dart", "hash": "92704d1a21b1793d6070f7bee27bfe68"}, {"path": "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-ebybunlcvccxiqanqwotqbhzsjgz/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/AssetManifest.json", "hash": "2efbb41d7877d10aac9d091f58ccd7b9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/pop_scope.dart", "hash": "3a11b2e3469f6d7a7d722f6df2c59dd8"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/dialog.dart", "hash": "3f80a1a01d62cd07e1ae5d34f9547b69"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/pinned_header_sliver.dart", "hash": "77aae98676ea4f6ece383935c23c1978"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/ink_sparkle.dart", "hash": "76e3e46c95f20cec7bf446ee56306fb8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.0.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/test_api-0.7.2/LICENSE", "hash": "3323850953be5c35d320c2035aad1a87"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/platform_channel.dart", "hash": "c7060506da9f5033615367bcfec355d9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/data/search_ellipsis.g.dart", "hash": "e822107ed1c00c270f7e9ccfe670576c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/edge_insets.dart", "hash": "36059bfe2991ae3c2a5d18941ef03857"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/search_view_theme.dart", "hash": "db01043236a4e15ffd4e3d8fad4c7cac"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/snapshot_widget.dart", "hash": "2c0bb1b05272ab6b5f631f34c4068679"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/beveled_rectangle_border.dart", "hash": "ce40486e207d35a8f247281a34f231b0"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/custom_layout.dart", "hash": "cb19324d7400b29cab877e6fd6aa0289"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/shaders/ink_sparkle.frag", "hash": "a0e89676ccae6cf3669483d52fa61075"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/sliver_list.dart", "hash": "86ba004de80b95197e3dbcab1233743b"}, {"path": "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-ebybunlcvccxiqanqwotqbhzsjgz/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/packages/cupertino_icons/assets/CupertinoIcons.ttf", "hash": "42d5bf7c22ac609351e84dbc39b12bf9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/checkbox_theme.dart", "hash": "********************************"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/banner.dart", "hash": "0cfa4ee77923dd3607c3e3bf35f3e070"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/reorderable_list.dart", "hash": "b2da3cfd818121daf62f41f3224c8a9f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/safe_area.dart", "hash": "366f1ebf48ef3c69b4e7a9ddcaa8f3ca"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/sliver_persistent_header.dart", "hash": "9d63de715fbdfcbad9064ab771762145"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/icon_button.dart", "hash": "946edf55d16a6affc23769f4e73e5fee"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/overlay.dart", "hash": "b9428aa8930bee03e47536cc9352bbc1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dynamiccolor/dynamic_scheme.dart", "hash": "7536ace8732469863c97185648bb15a9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/selectable_text.dart", "hash": "2dc7dd4788151c9148ad7b2a91058edf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_expressive.dart", "hash": "be096140df774ec827218c6fe69b80e5"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/draggable_scrollable_sheet.dart", "hash": "2f510aaa9b6e989e33c72b16c8b5b040"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/view.dart", "hash": "c04dc7f29cc9a9696d55c176099bcce6"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/ink_splash.dart", "hash": "2568c82b17a02e29a61c5e03a4eacefe"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/proxy_box.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/wrappers.dart", "hash": "91e47ed79ad65391642894923c520b26"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/shortcuts.dart", "hash": "0c9067d0b8afe6ac1c8b326551773709"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/user_accounts_drawer_header.dart", "hash": "00b3d6ec778c057356a8e0f99a4ff588"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scroll_position_with_single_context.dart", "hash": "faf60c9ef2ac54223911b10e9cf69c29"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/_timeline_io.dart", "hash": "90f70ffdd26c85d735fbedd47d5ad80b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/tap_and_drag.dart", "hash": "27fc926793fa8892a2acc5b7ebde35d9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/debug.dart", "hash": "17fec0de01669e6234ccb93fc1d171f2"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/rounded_rectangle_border.dart", "hash": "d10317bd2ff80b1a8f0f01907d62334c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.11.0/LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/eager.dart", "hash": "fe75cb9d73a87bf59cabc3af4d5072cb"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/text_scaler.dart", "hash": "fdb8e71a63736d013545f8830dc5c495"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/slotted_render_object_widget.dart", "hash": "fd48427e65c5910cbba1fc3e4e57cfcb"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/material_state_mixin.dart", "hash": "d9f9f2488723c1e03b8804bbeb41be03"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/raw_keyboard_listener.dart", "hash": "d8f8a80ad0c05f281d58e8f9e20b8b14"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.1/LICENSE", "hash": "901fb8012bd0bea60fea67092c26b918"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/serialization.dart", "hash": "41bd294b2c2eb1b089ab65341e92fd83"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/data_table.dart", "hash": "59939c42d2baae074e7123d552a36deb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/combined_wrappers/combined_list.dart", "hash": "5b894ae18be3e2442a34288833184ca9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/painting.dart", "hash": "443c2884ba8c15b3369e06601ffd88f7"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/menu_bar_theme.dart", "hash": "438d55cb1016f68c4db0da87b19ac82f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/bottom_app_bar_theme.dart", "hash": "69da3c88c748658e0c9e7216fd9398cb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.0.17/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/responsive_framework-0.2.0/lib/responsive_framework.dart", "hash": "7e8404a377f57878d7aadcd28f9b47f1"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/tooltip_visibility.dart", "hash": "5f94dbea71a53ba72600c479a41fa013"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/motion.dart", "hash": "374f899d15352be34ce61fd5243bed08"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/theme.dart", "hash": "f0561e97f70c3b1cf5f113f21d51fa39"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/isolates.dart", "hash": "d76b7e85f343a184365107019b8117b8"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scroll_position.dart", "hash": "6d3415786ad91086c9ad871e33c72e31"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/drawer_theme.dart", "hash": "e5bfa9fa388f0c5d8a91c7d5bd969b19"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/list_section.dart", "hash": "2c42d9101e59a8e036ec031c0aeaaf08"}, {"path": "/Users/<USER>/Documents/On-going/Flutter/Vguard/vguard-multiplatform-sample-SDK410/.dart_tool/flutter_build/efda113092a9e81ac9f0b9b3917e64ba/native_assets.yaml", "hash": "e7fd2fda36f01436b831ca47fe61fec3"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/radio_theme.dart", "hash": "1d2e0b9bdfb7d8463b27b487bf96ad46"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/material_localizations.dart", "hash": "17f28ff1f00166c9ef3955185daf21e0"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/debug.dart", "hash": "d4b68da22867b9c51c88acc54eab3198"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/divider_theme.dart", "hash": "b794bf7c553a2a0acab8dbfef6b0af0e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/sliver.dart", "hash": "5e2ac9d4e8f1c78581cc8aefa3d34c63"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/scheduler/ticker.dart", "hash": "84bcf5111497cf7e8f1447797932cbf0"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/constants.dart", "hash": "a22042c948166ba677133268fafc4b41"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/desktop_text_selection_toolbar.dart", "hash": "934a432cbf7baeb2d81ef25a49c36e1f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/raw_keyboard_web.dart", "hash": "4a3809e55c89557f70a08060ea2102a8"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/annotations.dart", "hash": "b092b123c7d8046443429a9cd72baa9a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/union_set_controller.dart", "hash": "f301af2d0392296f456363085becbf47"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.3.0/lib/characters.dart", "hash": "188d03c92376ce139ce247b0f9b0946e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/raw_keyboard_macos.dart", "hash": "70b3e472309bc151209b07238c849c38"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/pointer_signal_resolver.dart", "hash": "60c553948e23fc8fb2ba2dfb3e7ec153"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/quantizer_wu.dart", "hash": "c0da8171c63f0ab4e822dd094fc2c595"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/text_theme.dart", "hash": "aca21d4a3c0075a4d0109e79e87d7f21"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/debug.dart", "hash": "dbb0bb20c79bcea9397c34e3620c56c3"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/will_pop_scope.dart", "hash": "347ca56667b68d9d66174f8200b4505e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/data/home_menu.g.dart", "hash": "f183c429d3db89b9c97dfacaa85f09c3"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/navigator.dart", "hash": "7ae2142321839f48597dced1087444b1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.0.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/constants.dart", "hash": "aa4b5c0cdb6a66685350611b29ca9d38"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/card.dart", "hash": "4d2acf9063a0c90341c7af78e293a937"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scrollable.dart", "hash": "820396e431a6e00298858ae0931f50a7"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/physics/spring_simulation.dart", "hash": "d2fafa799f53bac4cb844e60b40a10f7"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/ink_well.dart", "hash": "b064d05257802d1c2555867b7817d23b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/rotated_box.dart", "hash": "********************************"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/debug.dart", "hash": "0f366f928cc93bceb62726d94a7bc889"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/dismissible.dart", "hash": "0f351e2163f0c6ea9f3ec5eb8881ea17"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/text_selection_toolbar_layout_delegate.dart", "hash": "45621326af8ac20233a5cf51cb1f05ce"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/animated_icons_data.dart", "hash": "ac08cb84358e3b08fc1edebf575d7f19"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/object.dart", "hash": "a22f8a00622b28aaa715b7587b658468"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/chip.dart", "hash": "aca1e7d1415fbff33606680b7d276d47"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/snack_bar.dart", "hash": "0127a67d8f96461d1bf6986dcb6260ee"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/keyboard_key.g.dart", "hash": "87f486583cb723b203e4e0d12d1958a1"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/text_formatter.dart", "hash": "e470881e8e68d9842928c6fa1a889596"}, {"path": "/Users/<USER>/Documents/On-going/Flutter/Vguard/vguard-multiplatform-sample-SDK410/.dart_tool/flutter_build/efda113092a9e81ac9f0b9b3917e64ba/program.dill", "hash": "fa7d8a004df63ba3e9dd9824205b7d11"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/shape_decoration.dart", "hash": "bee3197c0ad106b501694062a10e457a"}, {"path": "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-ebybunlcvccxiqanqwotqbhzsjgz/Build/Products/Debug-iphonesimulator/Flutter.framework/Flutter", "hash": "0347941473b2623a3272e328962ca967"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/navigation_drawer_theme.dart", "hash": "098ef2cc21af375e75e3fa80f2c8f12f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/change_notifier.dart", "hash": "14e0c453a6ee4b91f65cab141d997a88"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/sliver_grid.dart", "hash": "ab825e70224c09e49073e60d462f88fc"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/localizations.dart", "hash": "2464c23232ce73eb96d3fba785a62215"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/expand_icon.dart", "hash": "74ba66d118a95cad3da88b944da7a9dc"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/font_loader.dart", "hash": "8a899256e5ac320579b269ee9b2567a8"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/box_border.dart", "hash": "********************************"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/sliver.dart", "hash": "8ace72acd09f9f3961b7f3bec5be3056"}, {"path": "/Users/<USER>/Documents/On-going/Flutter/Vguard/vguard-multiplatform-sample-SDK410/.dart_tool/flutter_build/dart_plugin_registrant.dart", "hash": "d3ea5d5745f587231f3a22d40830b10e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/text_selection_toolbar.dart", "hash": "2f2ab157e82e8fdf20b247a00e5bde3c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/scheduler/service_extensions.dart", "hash": "6656ba0c69fefef80b8cae101896c029"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/decoration_image.dart", "hash": "7ffc0b0f4fae5d61fd47de2d122548eb"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/text_painter.dart", "hash": "d6345e12daac6b13e85a13629c3fddc7"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/text_selection_theme.dart", "hash": "eb2a941e76ef3aaf9ff856a5d93e0f7e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/shifted_box.dart", "hash": "********************************"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/switch_list_tile.dart", "hash": "65e6240e049c500eeb0bdff33155dfba"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_content.dart", "hash": "78e53d9a4963c0d19c5ea355a0946e5d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/borders.dart", "hash": "a5f06e177b702e5089a03bb9d18fc7fe"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/image_provider.dart", "hash": "b7a56c40e458c896ddfcb63f1b7aad62"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/desktop_text_selection_toolbar.dart", "hash": "4c75638ad31731ec9908b311ea075a5c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/button.dart", "hash": "75c340e47044712f389010dc4a220a3f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/data/list_view.g.dart", "hash": "0eae8cad9d933f0478d8387400def317"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/display_feature_sub_screen.dart", "hash": "1b935c75a1104936d71c6a3c0346c7a1"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/data/ellipsis_search.g.dart", "hash": "c9c0ff593fcabc29c3234b4e1bf2ac38"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/text_form_field_row.dart", "hash": "5a00bebaee1a7921574914f7dbdfff66"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/sliver_multi_box_adaptor.dart", "hash": "********************************"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/text_button.dart", "hash": "84126bd35d5680f3c48903776fb5162e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/mouse_tracking.dart", "hash": "8e493e051c6c0cbe792781a7574a8f26"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/iterable_extensions.dart", "hash": "040a16c5fccfea5a33d4c771c93003c2"}, {"path": "/Users/<USER>/Documents/On-going/Flutter/Vguard/vguard-multiplatform-sample-SDK410/pubspec.yaml", "hash": "50373160e6cedc5a9b6d878241450c94"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/image.dart", "hash": "9559118068f6ba981881a07626ef2d3f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/switch.dart", "hash": "f37381ef6280c57820b3aa6cbccb1f31"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/vector4.dart", "hash": "299bd3979d7999412945ac4e3199cdcf"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/desktop_text_selection.dart", "hash": "d34b1e33e7604b54b656d4c7471ad8a1"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/bottom_navigation_bar.dart", "hash": "44fe04a4d23a1e0df1470c5c0eacfddf"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/single_child_scroll_view.dart", "hash": "994baa57aed6041f4f36e7119938923a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/responsive_framework-0.2.0/lib/responsive_wrapper.dart", "hash": "6f9d822af5f7680fd9c303cdb9b799f1"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/theme_data.dart", "hash": "69e6b2a0e521311c4d86f8d7e93e29ed"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/chip_theme.dart", "hash": "4eb5a88d2cb5e6153b082794c6d39409"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/default_text_editing_shortcuts.dart", "hash": "72c0cf2358f026290fb717e74a910900"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/system_sound.dart", "hash": "2dd7e3b55dc8a0ddfeee22f0119b0082"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/physics/friction_simulation.dart", "hash": "d97019cfa3be6371779fc0e65f2bc118"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/utils/color_utils.dart", "hash": "0938e0447f447ceb7d16477a0213ce2c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/semantics/debug.dart", "hash": "3fd33becc9141d8a690c4205c72c5d40"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/platform_view.dart", "hash": "84a64086b6a56b4fac2100947a29c58d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/scale.dart", "hash": "4046f679f31351f52629d1b9f22e8b6c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter_tools/lib/src/build_system/targets/common.dart", "hash": "b5a5a0c9d78c8329f5d91094bfd4b820"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/colors.dart", "hash": "bee2e1aabab40248f6e4b4b8bccea509"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/bitfield.dart", "hash": "d235f51d48e43d80a46b35d3ac1a7135"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/desktop_text_selection_toolbar_button.dart", "hash": "af3cac4b25350f32615ddef14a0beb6c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/decorated_sliver.dart", "hash": "fe0a1ebd99d59b4024033158607723bf"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/tab_indicator.dart", "hash": "9537dbc5437603a8b66a7dae56c9875c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/raw_keyboard_windows.dart", "hash": "0a1a80151674cfd91279677d9f016bf2"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/orientation_builder.dart", "hash": "2df422a56d9988b696a9b0950f28bee3"}, {"path": "/Users/<USER>/development/flutter/packages/flutter_tools/lib/src/build_system/tools/shader_compiler.dart", "hash": "e38fe6e1f0ca30c3ce96e100ad684b22"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/binary_messenger.dart", "hash": "94f8cadcbb0bd065c1a70e483d849960"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/page_view.dart", "hash": "a8a5b571b1af5fc1c05c3941e3463592"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/predictive_back_page_transitions_builder.dart", "hash": "d113d4a197cc407c1012ca612db651ef"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/fade_in_image.dart", "hash": "537b299919c5bd3621a1af62db0c07a1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/opengl.dart", "hash": "9e22ead5e19c7b5da6de0678c8c13dca"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_tonal_spot.dart", "hash": "75f947f0ba87a0789a3ef91542bbc82c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/intersection_result.dart", "hash": "789e79772bba1132b3efdb60636a3ccb"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/box.dart", "hash": "********************************"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/list_wheel_scroll_view.dart", "hash": "1b59b4ab9530ab7549ca3c53173f4814"}, {"path": "/Users/<USER>/Documents/On-going/Flutter/Vguard/vguard-multiplatform-sample-SDK410/ios/Runner/Info.plist", "hash": "974a78c8acbb74d63df804528250dac4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/table.dart", "hash": "aa6152a8dc858cd16cf240ebfa31d605"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/segmented_button_theme.dart", "hash": "2fd858cebb63269bf83de20ec9b36762"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/icon_data.dart", "hash": "bf4d44ff5dca3de072782665509d0a7b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/media_query.dart", "hash": "b2a01f75c9e7125dda3e9461005b5765"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/implicit_animations.dart", "hash": "a4d5362f2c8444701c1e293b85fd003a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/quantizer_map.dart", "hash": "b6bcae6974bafba60ad95f20c12c72b9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/aabb2.dart", "hash": "f8fb1733ad7ae37b3d994f6f94750146"}, {"path": "/Users/<USER>/development/flutter/bin/cache/pkg/sky_engine/LICENSE", "hash": "b7add65f976680b338d238c4db63c68c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/system_chrome.dart", "hash": "90c8a81d181140ffdcdb8601cdf56207"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/no_splash.dart", "hash": "8217a1327affdcc17e4e9789ac490e7a"}, {"path": "/Users/<USER>/Documents/On-going/Flutter/Vguard/vguard-multiplatform-sample-SDK410/lib/MyHomePage.dart", "hash": "6066f4fc044d297355dab34a68278e4a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/autofill.dart", "hash": "2c3db13235dd0c924d1367692ec4ae1f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/list_body.dart", "hash": "0713268d2f4a4fe1a926f69964bcd39a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/text_field.dart", "hash": "d68d5ad46a579bc5f35d3da1c2f915ca"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/basic_types.dart", "hash": "435a09654d97bb25c33b68b9cde0a585"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.5/LICENSE", "hash": "2d0c70561d7f1d35b4ccc7df9158beed"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/menu_anchor.dart", "hash": "e66727f5e99e5f6a3d58e1bf0c47aa97"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/tooltip.dart", "hash": "0a7ba6f4900ffac5152d0a67876d6016"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/drawer_header.dart", "hash": "1786653a5a86ec6255f79137a3a33755"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/text_theme.dart", "hash": "666d237daabc113b3ba78fa363d47017"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/bottom_app_bar.dart", "hash": "aef3722f9d145aea6daf824f7e02a840"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/text_button_theme.dart", "hash": "e06184900e9722a899299b08b5b1d95c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scroll_configuration.dart", "hash": "1f07e42c78eef32e5fa787ecd1e72049"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/collections.dart", "hash": "bb393d088799db4aa3e456bd768f1687"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/arc.dart", "hash": "f8aff0b0ae4a957a0e3637de749e41a3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/collection.dart", "hash": "476383869aff7b87579a7753e47722d7"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/overflow_bar.dart", "hash": "3bdf4135a561f156f34a8ce9375819ea"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/date_picker.dart", "hash": "d28e7661d865b8f173e8d016a845d4fb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.2/lib/plugin_platform_interface.dart", "hash": "11f19da7f978409a768d58b1e5e6bfe7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/sphere.dart", "hash": "63473e31f03ea66a38affa41fd783752"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/grid_tile_bar.dart", "hash": "4f36e38eaf3608ec18c70c13942510bd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.5/LICENSE", "hash": "f721b495d225cd93026aaeb2f6e41bcc"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/desktop_text_selection_toolbar_button.dart", "hash": "1dc7dcdd70674a9f80245280f277e7ff"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/theme.dart", "hash": "3907ade9ce8b9e16265c3ebdff6cc132"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/radio_list_tile.dart", "hash": "f9d2ca632cbf0232de1a3b8826d4286f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/proxy_sliver.dart", "hash": "81f395ba7a262f5e3f75cc8ce6580d0b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/stack.dart", "hash": "3fb251a2c503ed05e490e8bf688e4fae"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/animation/listener_helpers.dart", "hash": "55380226455ea534ad3f21ab09fa4cae"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/stack_frame.dart", "hash": "e7fa4c3593bdfbc187766bd200e60599"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.1.0/lib/link.dart", "hash": "a03cc6311acd362bf8e7168e8d31da05"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/expansion_tile.dart", "hash": "4235d162e6bfe281428a9a62a1077806"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scroll_notification.dart", "hash": "fc064178d31756adc0c680657e2a10d6"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/raw_keyboard.dart", "hash": "033f66ce41fadd1cb1e04ea24214f9c3"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/debug_overflow_indicator.dart", "hash": "2c45e8b9705774232ea4fe1cf5c47b30"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/router.dart", "hash": "b294af2c4f13d55aba2bf44369a7ace3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/hct/viewing_conditions.dart", "hash": "cb0d5b80330326e301ab4d49952b2f34"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/circle_border.dart", "hash": "80df17b3f631d4ab4bd83a5ccb76a875"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/text_selection_toolbar_anchors.dart", "hash": "e4bd74894ed2108af4000673a2a92326"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/icon_button_theme.dart", "hash": "76e270c31be8244f4a49b954bba9c76d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/animated_size.dart", "hash": "25e50c0fbe710a5f7a590c3c26703f60"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/widget_span.dart", "hash": "62877c9dc6386e2c18c23df696e12753"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/_background_isolate_binary_messenger_io.dart", "hash": "170fe4655f45b54388ab850399d92895"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.15.0/lib/meta.dart", "hash": "8042ca366a626884c0e89628875af940"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/switch.dart", "hash": "7256737065bf49dbff23e83665dfb80e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/stepper.dart", "hash": "2905f1d86a6968256c390e989a477413"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/drag_target.dart", "hash": "fc74c3e85989d324a76e1a8a2d3f6dea"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/hct/hct.dart", "hash": "596fb2e55b1ff1662e4bd67461fdc89d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scroll_view.dart", "hash": "6c4f7ea6fca95237c26fda84192bc406"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/widgets.dart", "hash": "8c6dc36f670f9b9a09f5f9747abd11e5"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/binding.dart", "hash": "52e418c1649b02d8f12083c6ece3f93c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/layer.dart", "hash": "3c4c972da73c02c19c8597669d26a470"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/matrix3.dart", "hash": "447b270ddd29fa75f44c389fee5cadd1"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/button_bar_theme.dart", "hash": "b79eb4cc037c18412c9e228e1974783f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/bottom_sheet.dart", "hash": "b5dae33166e22586a2d2fd15b559099b"}, {"path": "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-ebybunlcvccxiqanqwotqbhzsjgz/Build/Products/Debug-iphonesimulator/App.framework/flutter_assets/fonts/MaterialIcons-Regular.otf", "hash": "e7069dfd19b331be16bed984668fe080"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/drawer.dart", "hash": "be15261e73d394f99ecb0d5e609aafac"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/node.dart", "hash": "dff97db228356561674b5f690cd54f41"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/undo_manager.dart", "hash": "********************************"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/spell_check.dart", "hash": "ece5ee01a742a658ea5b2ee2a328c418"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.1.0/lib/method_channel_url_launcher.dart", "hash": "351ed98071b53d3c2e98d376f2a65a74"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/expansion_tile_theme.dart", "hash": "8166d4859a89eef9e25697932c522bce"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/navigator_pop_handler.dart", "hash": "c662670b9313c57d5d61b85f539a5797"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.2/LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.3.0/lib/src/characters.dart", "hash": "21bf6725b1fc374f03ae5b2cb46bd95b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/bottom_navigation_bar_item.dart", "hash": "7e69fcdf387be2ef9513d34ba4bbbbdb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/ray.dart", "hash": "********************************"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/multidrag.dart", "hash": "8d4df3ef11f873038812b16364638706"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/badge.dart", "hash": "2aca9cb812aa2f6f590b65b326ed333e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/comparators.dart", "hash": "d1410f48ac374235aaad55cba40bc4be"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/sliver_fill.dart", "hash": "c1ee64b6c171e90ac92885883f0f9ce4"}]}