{"info": "This is a generated file; do not edit or check into version control.", "plugins": {"ios": [{"name": "uni_links", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uni_links-0.5.1/", "native_build": true, "dependencies": []}, {"name": "url_launcher_ios", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.0.17/", "native_build": true, "dependencies": []}], "android": [{"name": "uni_links", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uni_links-0.5.1/", "native_build": true, "dependencies": []}, {"name": "url_launcher_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.0.17/", "native_build": true, "dependencies": []}], "macos": [{"name": "url_launcher_macos", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.0.1/", "native_build": true, "dependencies": []}], "linux": [{"name": "url_launcher_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.0.1/", "native_build": true, "dependencies": []}], "windows": [{"name": "url_launcher_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.0.1/", "native_build": true, "dependencies": []}], "web": [{"name": "uni_links_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uni_links_web-0.1.0/", "dependencies": []}, {"name": "url_launcher_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_web-2.0.12/", "dependencies": []}]}, "dependencyGraph": [{"name": "uni_links", "dependencies": ["uni_links_web"]}, {"name": "uni_links_web", "dependencies": []}, {"name": "url_launcher", "dependencies": ["url_launcher_android", "url_launcher_ios", "url_launcher_linux", "url_launcher_macos", "url_launcher_web", "url_launcher_windows"]}, {"name": "url_launcher_android", "dependencies": []}, {"name": "url_launcher_ios", "dependencies": []}, {"name": "url_launcher_linux", "dependencies": []}, {"name": "url_launcher_macos", "dependencies": []}, {"name": "url_launcher_web", "dependencies": []}, {"name": "url_launcher_windows", "dependencies": []}], "date_created": "2024-11-13 13:28:48.744267", "version": "3.24.4", "swift_package_manager_enabled": false}