{"folders": [{"name": "vguard-multiplatform-sample-SDK410/ios/Runner.xcworkspace", "path": "../.."}], "settings": {"files.exclude": {"vosvguardfluttersample.iml": true, "test": true, "pubspec.yaml": true, "pubspec.lock": true, "lib": true, "firebase-debug.log": true, "build": true, "android": true, "analysis_options.yaml": true, "README.md": true, ".packages": true, ".flutter-plugins-dependencies": true, ".flutter-plugins": true, ".dart_tool": true, ".vscode/xcode/fifo": true, ".vscode/xcode/bundles": true}, "search.exclude": {"vosvguardfluttersample.iml": true, "test": true, "pubspec.yaml": true, "pubspec.lock": true, "lib": true, "firebase-debug.log": true, "build": true, "android": true, "analysis_options.yaml": true, "README.md": true, ".packages": true, ".flutter-plugins-dependencies": true, ".flutter-plugins": true, ".dart_tool": true, ".vscode/xcode/fifo": true, ".vscode/xcode/bundles": true}, "swift.autoGenerateLaunchConfigurations": false, "swift.disableAutoResolve": true, "swift.sourcekit-lsp.disable": true, "swift.disableSwiftPackageManagerIntegration": true, "swift.searchSubfoldersForPackages": false}, "extensions": {"unwantedRecommendations": ["sswg.swift-lang"]}}