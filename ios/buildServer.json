{"name": "xcode build server", "version": "0.2", "bspVersion": "2.0", "languages": ["c", "cpp", "objective-c", "objective-cpp", "swift"], "argv": ["/Users/<USER>/.vscode/extensions/fireplusteam.vscode-ios-0.5.11/xcode-build-server/xcode-build-server"], "workspace": "/Users/<USER>/Documents/On-going/Flutter/Vguard/vguard-multiplatform-sample-SDK410/ios/Runner.xcworkspace", "build_root": "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-ebybunlcvccxiqanqwotqbhzsjgz", "scheme": "Runner", "kind": "xcode"}