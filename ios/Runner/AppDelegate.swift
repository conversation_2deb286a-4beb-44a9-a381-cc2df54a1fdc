import UIKit
import Flutter

// MARK: - Protocols
protocol UILogDelegate {
    func printUILogs(_ logs: String)
}

// MARK: - Main AppDelegate Class
@main
@objc class AppDelegate: FlutterAppDelegate, FlutterStreamHandler {
    
    // MARK: - Properties
    private var _eventSink: FlutterEventSink?
    private var _pendingEvents: [[String: Any]] = []
    var vGuardManager: VGuardManager! = nil
    var uiLogDelegate: UILogDelegate?
    var resetAttemptCount: Int = 0
    private let maxResetAttempts = 1
    private var isResetting = false
    
    // MARK: - Flutter Event Stream Handler
    func onListen(withArguments arguments: Any?, eventSink events: @escaping FlutterEventSink) -> FlutterError? {
        _eventSink = events
        printLog("onListen \(String(describing: _eventSink))")
        
        // Send any pending events that occurred before the sink was ready
        if !_pendingEvents.isEmpty {
            DispatchQueue.main.async { [weak self] in
                guard let self = self else { return }
                for event in self._pendingEvents {
                    events(event)
                }
                self._pendingEvents.removeAll()
            }
        }
        
        return nil
    }
    
    func onCancel(withArguments arguments: Any?) -> FlutterError? {
        _eventSink = nil
        return nil
    }
    
    // MARK: - Event Sending Methods
    private func sendVguardEvent(eventName: String, data: Any) {
        let serializableData = makeSerializable(data)
        let event: [String: Any] = ["name": eventName, "data": serializableData]
        
        if let eventSink = _eventSink {
            DispatchQueue.main.async {
                eventSink(event)
            }
        } else {
            printLog("EventSink not available for event: \(eventName)")
            // Store the event to send later when sink becomes available
            _pendingEvents.append(event)
        }
    }
    
    private func makeSerializable(_ data: Any) -> Any {
        switch data {
        case let error as Error:
            return ["message": error.localizedDescription]
        case let dict as [String: Any]:
            return dict.mapValues { $0 is CustomStringConvertible ? String(describing: $0) : $0 }
        case let array as [Any]:
            return array.map { $0 is CustomStringConvertible ? String(describing: $0) : $0 }
        case let status as VGUARD_STATUS:
            return String(describing: status)
        case let status as VOS_STATUS:
            return String(describing: status)
        default:
            return String(describing: data)
        }
    }
    
    // MARK: - Utility Methods
    func sentLogEvent(logText: String) {
        NSLog("^\(logText)")
    }
    
    func simulate8iOS() {
        let fileManager = FileManager.default
        if let documentsDirectory = fileManager.urls(for: .documentDirectory, in: .userDomainMask).first {
            let fileURL = documentsDirectory.appendingPathComponent("IRK.enc")
            
            do {
                if fileManager.fileExists(atPath: fileURL.path) {
                    try fileManager.removeItem(at: fileURL)
                    printLog("simulate8iOS: Deleted file at path: \(fileURL.path)")
                    sendVguardEvent(eventName: "[log]", data: ["log": "simulate8iOS: Deleted file at path: \(fileURL.path)"])
                } else {
                    printLog("can't find IRK.enc, maybe it's already deleted?")
                    sendVguardEvent(eventName: "[log]", data: ["log": "can't find IRK.enc, maybe it's already deleted?"])
                }
            } catch {
                printLog("Error deleting IRK.enc file at path: \(fileURL.path) - \(error)")
            }
        }
    }
    
    // MARK: - App Lifecycle Methods
    override func applicationDidBecomeActive(_ application: UIApplication) {
        printLog("applicationDidBecomeActive")
        VkeyConfiguration()
    }
    
    override func application(
        _ application: UIApplication,
        didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
    ) -> Bool {
        printLog("didFinishLaunchingWithOptions")
        
        GeneratedPluginRegistrant.register(with: self)
        
        let controller : FlutterViewController = window?.rootViewController as! FlutterViewController
        
        // Setup Flutter Event Channel
        let eventLogChannel = FlutterEventChannel(name: "com.vkey.vguard/vguardreceiver", binaryMessenger: controller.binaryMessenger)
        eventLogChannel.setStreamHandler(self)
        
        // Setup Flutter Method Channel
        let vguardChannel = FlutterMethodChannel(name: "com.vkey.vguard/vguardplugin", binaryMessenger: controller.binaryMessenger)
        
        vguardChannel.setMethodCallHandler({ [weak self](call: FlutterMethodCall, result: @escaping FlutterResult) -> Void in
            guard let self = self else {
                result(FlutterError(code: "UNAVAILABLE", message: "AppDelegate deallocated", details: nil))
                return
            }
            
            switch call.method {
            case "startVGuard":
                self.VkeyConfiguration()
                result(nil)
            case "VkeyConfiguration":
                self.VkeyConfiguration()
                result(nil)
            case "simulate8iOS":
                self.simulate8iOS()
                result(nil)
            case "getTroubleshootingID":
                result(self.getTroubleshootingID())
            default:
                result(FlutterMethodNotImplemented)
            }
        })
        
        return super.application(application, didFinishLaunchingWithOptions: launchOptions)
    }
}

// MARK: - VGuard Management Extension
extension AppDelegate {
    
    func getTroubleshootingID() -> String {
        if (vGuardManager != nil) {
            let ID = vGuardManager.getTroubleshootingId()
            printLog("getTroubleshootingId: \(ID)")
            return ID
        }
        return "not yet init vGuardManager"
    }
    
    func printLog(_ logs: String) {
        print("^\(logs)")
    }
    
    func VkeyConfiguration() {
        if (vGuardManager != nil) {
            vGuardManager.start()
        } else {
            
            // Initialize VGuard manager
            vGuardManager = VGuardManager.shared()
            vGuardManager.delegate = self
            
            // Setup TI/TLA URLs in background
            DispatchQueue.global(qos: .userInitiated).async { [weak self] in
                guard let self else { return }
                vGuardManager.setThreatIntelligenceServerURL("https://stg-cloud.v-key.com")// NOTE: Ti
                VosWrapper.setLoggerBaseUrl("https://stg-cloud.v-key.com")//NOTE: TLA
            }
            
            // Setup delegates
            VGuardExceptionHandler.sharedManager()?.delegate = self
            VGuardThreats.sharedModule()?.delegate = self
            
            // Initialize and start
            printLog("initialize VGuard.....")
            sendVguardEvent(eventName: "[log]", data: ["log": "initialize VGuard..... "])
            vGuardManager.initializeVGuard()
            vGuardManager.start()
        }
    }
}

// MARK: - VGuard Reset Logic Extension
extension AppDelegate {
    
    private func canAttemptReset() -> Bool {
        return resetAttemptCount < maxResetAttempts && !isResetting
    }
    
    private func performResetLogic() {
        isResetting = true
        resetAttemptCount += 1
        
        printLog("Triggering reset attempt \(resetAttemptCount)")
        sendVguardEvent(eventName: "[log]", data: ["log": "Reset attempting ... \(resetAttemptCount)"])
        
        vGuardManager?.resetVOSTrustedStorage()
        vGuardManager = nil
        
        // Re-initialize after reset
        VkeyConfiguration()
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) { [weak self] in
            self?.isResetting = false
        }
    }
}

// MARK: - VGuard Delegate Implementation
extension AppDelegate: VGuardManagerProtocol, VGuardThreatsDelegate, VGuardExceptionHandlerProtocol {
    
    // MARK: - VOS Status Handling
    func statusVOS(_ status: VOS_STATUS, withError error: Error?) {
        printLog("statusVOS: \(status) \(String(describing: error))")
        let statusString = (status == VOS_OK) ? "VOS_OK" : "VOS_NOTOK"
        
        if let error = error {
            let errorCode = (error as NSError).code
            
            // Handle reset in case vos trusted storage errors
            if (errorCode == -3 || errorCode == -5 || errorCode == -8 && canAttemptReset()) {
                sendVguardEvent(eventName: "[log]", data: ["log": "\(statusString) with vos trusted storage error: \(errorCode)"])
                performResetLogic()
            } else {
                sendVguardEvent(eventName: "[statusVOS]", data: ["status": "\(statusString)", "error": errorCode])
            }
        } else {
            // safe case
            vGuardManager?.allowsArbitraryNetworking(true) // NOTE: enable this code to turn off SSL Pinning feature
            sendVguardEvent(eventName: "[statusVOS]", data: ["status": statusString])
        }
    }
    
    // MARK: - VGuard Status Handling
    func statusVGuard(_ status: VGUARD_STATUS, withError error: Error?) {
        printLog("statusVGuard")
    }
    
    // MARK: - VGuard Initialization Callback
    func vGuardDidFinishInitializing(_ status: Bool, withError error: Error?) {
        printLog("vGuardDidFinishInitializing status: \(status) error: \(String(describing: error))")
        
        let strStatus = status ? "SUCCESS" : "FAILED" // Selected code with proper structure
        
        if error != nil {
            let errorCode = (error! as NSError).code
            if (errorCode >= 200000 && errorCode < 30000 && errorCode != 20035) {// ignore 20035 since it was handled in statusVOS callback (-8)
                sendVguardEvent(eventName: "[vGuardDidFinishInitializing]", data: ["status": strStatus, "error": String(errorCode)])
            }
        } else {
            sendVguardEvent(eventName: "[vGuardDidFinishInitializing]", data: ["status": strStatus])
        }
    }
    
    // MARK: - SSL Error Detection
    func vGuardDidDetectSSLError(_ error: Error) {
        printLog("vGuardDidDetectSSLError")
        sendVguardEvent(eventName: "[vGuardDidDetectSSLError]", data: String(describing: error))
    }
    
    // MARK: - Exception Handling
    func vGuardExceptionHandler(_ exception: NSException!) {
        printLog("vGuardExceptionHandler")
        sendVguardEvent(eventName: "[vGuardExceptionHandler]", data: String(describing: exception))
    }
    
    // MARK: - Screen Sharing Detection
    func vGuardDidDetectScreenSharing() {
        printLog("vGuardDidDetectScreenSharing")
        sendVguardEvent(eventName: "[vGuardDidDetectScreenSharing]", data: ["status": true])
    }
    
    // MARK: - Threat Scanning
    func vGuardScan(_ threatsArray: [Any]){
        printLog("vGuardScan: \(String(describing: threatsArray))")
        sendVguardEvent(eventName: "[vGuardScan]", data: ["threatsArray": String(describing: threatsArray)])
    }
    
    func vGuardDidDetectThreats(_ threatsInfo: [AnyHashable : Any]) {
        printLog("vGuardDidDetectThreats: \(String(describing: threatsInfo))")
//         sendVguardEvent(eventName: "[vGuardDidDetectThreats]", data: String(describing: threatsInfo))
        //NOTE: you can get threats result in vGuardScan callback or vGuardDidDetectThreats, no need to handle both callback
    }
}
