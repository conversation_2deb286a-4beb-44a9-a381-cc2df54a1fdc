/*
 * * * * * * * * * * * * *
 * Copyright(c) {2020} lnt<<EMAIL>>.  All Rights Reserved.
 * * * * * * * * * * * * *
 * __MyHomePage__.dart
 *
 * Created on 05/05/2022 
 *
 * {Insert class description here}
 * 
 */

import 'dart:async';
import 'dart:io';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class MyHomePage extends StatefulWidget {
  const MyHomePage({Key? key, required this.title}) : super(key: key);

  // This widget is the home page of your application. It is stateful, meaning
  // that it has a State object (defined below) that contains fields that affect
  // how it looks.

  // This class is the configuration for the state. It holds the values (in this
  // case the title) provided by the parent (in this case the App widget) and
  // used by the build method of the State. Fields in a Widget subclass are
  // always marked "final".

  final String title;

  @override
  State<MyHomePage> createState() => _MyHomePageState();
}

class _MyHomePageState extends State<MyHomePage> {
  // ==================== CONSTANTS ====================
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  static const kVGuardPluginMethod = 'com.vkey.vguard/vguardplugin';
  static const kVGuardPluginEvent = 'com.vkey.vguard/vguardreceiver';
  static const vguardplugin = MethodChannel(kVGuardPluginMethod);
  
  // ==================== STATE VARIABLES ====================
  StreamSubscription<dynamic>? _vguardEventSubscription;
  String _logs = '';
  bool _isInitialized = false;

  // ==================== LIFECYCLE METHODS ====================
  @override
  void initState() {
    super.initState();
    _setupVGuardEventListener();
  }

  @override
  void dispose() {
    _vguardEventSubscription?.cancel();
    super.dispose();
  }

  // ==================== EVENT HANDLING ====================
  void _setupVGuardEventListener() {
    const eventChannel = EventChannel(kVGuardPluginEvent);
    
    _vguardEventSubscription = eventChannel.receiveBroadcastStream().listen(
      _handleVGuardEvent,
      onError: (error) => _addLog('VGuard event error: $error'),
    );
  }

  void _handleVGuardEvent(dynamic event) {
    if (event is Map) {
      final eventName = event['name']?.toString() ?? 'Unknown Event';
      final eventData = event['data'];
      
      switch (eventName) {
        // VOS Status Handling
        case '[statusVOS]':
          if (eventData is Map) {
            final status = eventData['status']?.toString() ?? '';
            
            if (status.contains('VOS_OK')) {
              setState(() {
                _isInitialized = true;
              });
              _addLog('VOS Status: OK - System is secure');
            } else if (status.contains('VOS_NOTOK')) {
              setState(() {
                _isInitialized = false;
              });
              if (eventData['error'] != null) {
                final errorCode = eventData['error']?.toString() ?? 'Unknown';
                _addLog('statusVOS Error: [$errorCode]');
                // NOTE: hanlde to show alert and quit app base on your business logic
              }
            }
          }
          break;
          
        // VGuard Status Handling  
        case '[statusVGuard]':
          if (eventData is Map) {
            final status = eventData['status']?.toString() ?? 'Unknown';
            _addLog('[statusVGuard]: $status');
          }
          break;
          
        // VGuard Initialization
        case '[vGuardDidFinishInitializing]':
          if (eventData is Map) {
            final status = eventData['status']?.toString() ?? '';
            if (status == 'SUCCESS') {
              _addLog('[vGuardDidFinishInitializing] true');
            } else {
              if (eventData['error'] != null) {
                final errorCode = eventData['error']?.toString() ?? 'Unknown';
                _addLog('vGuardDidFinishInitializing Error [$errorCode]');
                // NOTE: hanlde to show alert and quit app base on your business logic
              }
            }
          }
          break;
          
        // SSL Error Detection
        case '[vGuardDidDetectSSLError]':
          _addLog('[vGuardDidDetectSSLError]: ${eventData?.toString() ?? 'Unknown SSL error'}');
          // NOTE: hanlde to show alert and quit app base on your business logic
          break;
          
        // Exception Handling
        case '[vGuardExceptionHandler]':
          _addLog('[vGuardExceptionHandler]: ${eventData?.toString() ?? 'Unknown exception'}');
          // NOTE: hanlde to show alert and quit app base on your business logic
          break;
          
        // Screen Sharing Detection
        case '[vGuardDidDetectScreenSharing]':
        _addLog('Screen sharing detected!');
          // NOTE: hanlde to show alert and quit app base on your business logic
          break;
          
        // Threat Scanning
        case '[vGuardScan]':
          if (eventData is Map) {
            final threatsArray = eventData['threatsArray']?.toString() ?? 'No threats data';
            _addLog('[vGuardScan] found threats:');
            //NOTE: hanlde to show alert and quit app base on your business logic
            // hanlde based on threatTypedId
            // for iOS: 1000, 2000, 4000, ...
            _addLog(threatsArray);
          } else {
            _addLog('[vGuardScan] completed, no threat found');
          }
          break;
          
        // Log Events
        case '[log]':
          if (eventData is Map) {
            final logMessage = eventData['log']?.toString() ?? 'No log message';
            _addLog('Log: $logMessage');
          } else {
            _addLog('Log: ${eventData?.toString() ?? 'No log data'}');
          }
          break;
          
        // Default case for unknown events
        default:
          _addLog('Event: $eventName');
          _addLog('Data: ${eventData?.toString() ?? 'No Data'}');
          break;
      }
      
      if (!Platform.isIOS) _addLog('--------');
    } else {
      _addLog('Invalid event format: $event');
    }
  }

  // ==================== UTILITY METHODS ====================
  void _addLog(String message) {
    final timestamp = DateTime.now().toString().substring(11, 19);
    setState(() {
      _logs += '\n[$timestamp] $message';
    });
  }

  Future<T?> _safeInvokeMethod<T>(String method, [dynamic arguments]) async {
    try {
      final result = await vguardplugin.invokeMethod<T>(method, arguments);
      return result;
    } on PlatformException catch (e) {
      _addLog('Error in $method: ${e.message ?? e.code}');
      return null;
    }
  }

  // ==================== VGUARD OPERATIONS ====================
  Future<void> initVguard() async {
    if (_isInitialized) {
      _addLog('VGuard already initialized');
      return;
    }

    setState(() => _isInitialized = true);
    
    final method = Platform.isIOS ? 'VkeyConfiguration' : 'setupVGuard';
    final result = await _safeInvokeMethod(method);
    
    if (result != null) {
      _addLog('VGuard initialized successfully');
      if (!Platform.isIOS) _addLog('Result: $result');
    } else {
      setState(() => _isInitialized = false);
    }
  }

  Future<void> startScan() async {
    if (!_isInitialized) {
      _addLog('Please initialize VGuard first');
      return;
    }
    
    final method = Platform.isIOS ? 'startVGuard' : 'requestScan';
    final result = await _safeInvokeMethod(method);
    
    if (result != null) {
      _addLog('Scan started successfully');
      if (!Platform.isIOS) _addLog('Result: $result');
    }
  }

  Future<void> VkeyConfiguration() async {
    if (Platform.isIOS) {
      final result = await _safeInvokeMethod('VkeyConfiguration');
      if (result != null) {
        _addLog('VGuard configured successfully');
      }
    } else {
      _addLog('Configure not available on Android');
    }
  }

  // simulate8iOS
  Future<void> simulate8iOS() async {
    if (Platform.isIOS) {
      final result = await _safeInvokeMethod('simulate8iOS');
      if (result != null) {
        _addLog('Simulate 8iOS triggered');
      }
    }
  }

  Future<void> getTroubleshootingID() async {
    final result = await _safeInvokeMethod<String>('getTroubleshootingID');
    if (result != null) {
      _addLog('Troubleshooting ID: $result');
    }
  }

  // ==================== UI HELPER METHODS ====================
  Widget _buildActionButton({
    required String text,
    required VoidCallback? onPressed,
    bool isLoading = false,
  }) {
    return ElevatedButton(
      onPressed: isLoading ? null : onPressed,
      child: isLoading 
          ? const SizedBox(
              width: 16, 
              height: 16, 
              child: CircularProgressIndicator(strokeWidth: 2)
            )
          : Text(text),
    );
  }

  void _clearLogs() {
    setState(() => _logs = '');
  }

  // ==================== BUILD METHOD ====================
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: _scaffoldKey,
      appBar: AppBar(
        title: Text(widget.title),
        actions: [
          IconButton(
            icon: const Icon(Icons.clear),
            onPressed: _clearLogs,
            tooltip: 'Clear logs',
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            const Text(
              'VKey App Protection Test',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            
            // Action Buttons Row 1
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                Expanded(
                  child: _buildActionButton(
                    text: 'Init VGuard',
                    onPressed: initVguard,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildActionButton(
                    text: Platform.isAndroid ? 'Request Scan' : 'Start VGuard',
                    onPressed: startScan,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            
            // Action Buttons Row 2
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                Expanded(
                  child: _buildActionButton(
                    text: 'simulate8iOS',
                    onPressed: Platform.isIOS ? simulate8iOS : null,
                  ),
                ),
                Expanded(
                  child: _buildActionButton(
                    text: 'getTroubleshootingID',
                    onPressed: getTroubleshootingID,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            
            // Status Indicator
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: _isInitialized ? Colors.green.shade100 : Colors.red.shade100,
                borderRadius: BorderRadius.circular(4),
              ),
              child: Row(
                children: [
                  Icon(
                    _isInitialized ? Icons.check_circle : Icons.error,
                    color: _isInitialized ? Colors.green : Colors.red,
                    size: 16,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'VOS Status: ${_isInitialized ? "VOS_OK (Secure)" : "VOS_NOTOK (Error)"}',
                    style: TextStyle(
                      color: _isInitialized ? Colors.green.shade800 : Colors.red.shade800,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            
            // Logs Section Header with Clear Button
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Logs:',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                ),
                TextButton.icon(
                  onPressed: _clearLogs,
                  icon: const Icon(Icons.clear_all, size: 16),
                  label: const Text('Clear'),
                  style: TextButton.styleFrom(
                    foregroundColor: Colors.red,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Expanded(
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300),
                  borderRadius: BorderRadius.circular(8),
                  color: Colors.grey.shade50,
                ),
                child: SingleChildScrollView(
                  child: Text(
                    _logs.isEmpty ? 'No logs yet...' : _logs,
                    style: const TextStyle(
                      fontFamily: 'monospace',
                      fontSize: 12,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
